model collectCashStats {
  id                   String   @id @default(uuid()) @db.Uuid
  customerId           String   @map(name: "customer_id") @db.Uuid
  portfolioId          String   @map(name: "portfolio_id") @db.Uuid
  portfolioItemId      String   @map(name: "portfolio_item_id") @db.Uuid
  workflowId           String   @map(name: "workflow_id") @db.Uuid
  dealValue            Int      @map(name: "deal_value")
  installments         Int      @map(name: "installments") @default(1)
  originalDebt         Int      @map(name: "original_debt")
  status               String   @default("ACTIVE")
  createdAt            DateTime @default(now()) @map(name: "created_at")
  updatedAt            DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@index([customerId])
  @@index([portfolioId])
  @@index([portfolioItemId])
  @@index([workflowId])
  @@index([customerId, portfolioId])
  @@index([createdAt])
  @@map(name: "collect_cash_stats")
  @@schema("business_base")
}
