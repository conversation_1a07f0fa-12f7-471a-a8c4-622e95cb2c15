import { HttpStatus, Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient, Prisma } from '@prisma/client';
import { handlePrismaErrors } from '@common/exception/handlers/handle-prisma-errors.util';
import { PrismaException } from '@common/exception/types/PrismaException';
import { logger } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class PrismaService implements OnModuleInit, OnModuleDestroy {
  private _client: any; // Extended PrismaClient with custom middleware
  private static instance: PrismaService;

  constructor() {
    // Ensure singleton pattern to prevent multiple instances
    if (PrismaService.instance) {
      logger.warn('PrismaService instance already exists, reusing existing instance');
      return PrismaService.instance;
    }
    if (process.env.NODE_ENV === 'automated-tests' && global.prisma) {
      // In a test environment, reuse the global instance.
      this._client = global.prisma;
    } else {
      // En un entorno no de prueba, crea una nueva instancia
      this._client = new PrismaClient({
        log:
          process.env.NODE_ENV === 'automated-tests' || process.env.NODE_ENV === 'production'
            ? [
                { emit: 'stdout', level: 'error' },
                { emit: 'stdout', level: 'warn' },
              ]
            : [
                //{ emit: 'stdout', level: 'query' },
                { emit: 'stdout', level: 'error' },
                { emit: 'stdout', level: 'info' },
                { emit: 'stdout', level: 'warn' },
              ],
        // Connection pool configuration for AWS RDS
        // Note: Connection pool settings are configured via DATABASE_URL connection string parameters
        // Example: postgresql://user:password@host:port/db?connection_limit=10&pool_timeout=10
      }).$extends({
        query: {
          $allOperations: async ({ model, operation, args, query }) => {
            const traceId = CorrelationContextService.getTraceId();
            const context = CorrelationContextService.getContext();
            const startTime = Date.now();

            try {
              const result = await query(args);
              return result;
            } catch (error) {
              const duration = Date.now() - startTime;

              // Enhanced error logging for database failures
              logger.error('Database operation failed', {
                traceId,
                operation: `${model}.${operation}`,
                model,
                action: operation,
                duration: `${duration}ms`,
                error: error.message,
                errorCode: error.code,
                errorType: this.getDatabaseErrorType(error),
                errorMeta: this.sanitizeErrorMeta(error.meta),
                queryArgs: this.sanitizeQueryArgs(args),
                businessContext: context?.operation,
                stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
                timestamp: new Date().toISOString(),
                layer: 'DATABASE',
                severity: this.getErrorSeverity(error),
              });

              // Log connection pool status on connection errors
              if (this.isConnectionError(error)) {
                logger.error('Database connection error detected', {
                  traceId,
                  errorType: 'CONNECTION_ERROR',
                  error: error.message,
                  errorCode: error.code,
                  businessContext: context?.operation,
                  timestamp: new Date().toISOString(),
                  layer: 'DATABASE',
                });
              }

              if (
                error instanceof Prisma.PrismaClientKnownRequestError ||
                error.constructor.name === 'PrismaClientKnownRequestError'
              ) {
                throw handlePrismaErrors(error);
              }

              throw new PrismaException(
                {
                  message: 'Internal Server Error',
                  error: `Unexpected database error: ${error.message}`,
                },
                HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }
          },
          $allModels: {
            async create({ args, query }) {
              const result = await query(args);

              return result;
            },
          },
        },
      });
    }

    // Set the singleton instance
    PrismaService.instance = this;
    logger.info('PrismaService singleton instance created');
  }

  async onModuleInit() {
    if (process.env.NODE_ENV !== 'automated-tests' || !global.prisma) {
      try {
        await this._client.$connect();
        logger.info('Successfully connected to database');
      } catch (error) {
        logger.error('Failed to connect to database:', error);
        throw error;
      }
    }
  }

  async onModuleDestroy() {
    if (process.env.NODE_ENV !== 'automated-tests' || !global.prisma) {
      try {
        await this._client.$disconnect();
        logger.info('Successfully disconnected from database');
      } catch (error) {
        logger.error('Error disconnecting from database:', error);
        // Don't throw here to avoid preventing application shutdown
      }
    }
  }

  get client(): PrismaClient {
    return this._client;
  }

  get prisma() {
    return this._client;
  }

  private sanitizeQueryArgs(args: any): any {
    if (!args) return args;

    try {
      const sanitized = JSON.parse(JSON.stringify(args));
      const sensitiveFields = [
        'password',
        'token',
        'secret',
        'key',
        'hash',
        'salt',
        'authorization',
        'credential',
        'auth',
        'bearer',
        'apiKey',
      ];

      this.redactSensitiveFields(sanitized, sensitiveFields);

      // Truncate large query args to prevent log bloat
      const argsString = JSON.stringify(sanitized);
      if (argsString.length > 1000) {
        return {
          ...sanitized,
          _truncated: true,
          _originalSize: argsString.length,
          _note: 'Query args truncated for logging',
        };
      }

      return sanitized;
    } catch {
      return '[UNPARSEABLE_ARGS]';
    }
  }

  private sanitizeErrorMeta(meta: any): any {
    if (!meta) return meta;

    try {
      const sanitized = JSON.parse(JSON.stringify(meta));
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'hash'];
      this.redactSensitiveFields(sanitized, sensitiveFields);
      return sanitized;
    } catch {
      return '[UNPARSEABLE_META]';
    }
  }

  private redactSensitiveFields(obj: any, sensitiveFields: string[]): void {
    if (typeof obj !== 'object' || obj === null) return;

    for (const key in obj) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        this.redactSensitiveFields(obj[key], sensitiveFields);
      }
    }
  }

  private getResultCount(result: any): number | string {
    if (!result) return 0;

    if (Array.isArray(result)) {
      return result.length;
    }

    if (typeof result === 'object' && result.count !== undefined) {
      return result.count;
    }

    if (typeof result === 'object') {
      return 1;
    }

    return 'unknown';
  }

  private getDatabaseErrorType(error: any): string {
    if (!error.code) return 'UNKNOWN_ERROR';

    // Prisma error codes
    switch (error.code) {
      case 'P1000':
        return 'AUTHENTICATION_FAILED';
      case 'P1001':
        return 'CONNECTION_REFUSED';
      case 'P1002':
        return 'CONNECTION_TIMEOUT';
      case 'P1003':
        return 'DATABASE_NOT_FOUND';
      case 'P1008':
        return 'OPERATION_TIMEOUT';
      case 'P1009':
        return 'DATABASE_ALREADY_EXISTS';
      case 'P1010':
        return 'ACCESS_DENIED';
      case 'P1011':
        return 'TLS_CONNECTION_ERROR';
      case 'P2000':
        return 'VALUE_TOO_LONG';
      case 'P2001':
        return 'RECORD_NOT_FOUND';
      case 'P2002':
        return 'UNIQUE_CONSTRAINT_VIOLATION';
      case 'P2003':
        return 'FOREIGN_KEY_CONSTRAINT_VIOLATION';
      case 'P2004':
        return 'CONSTRAINT_VIOLATION';
      case 'P2005':
        return 'INVALID_VALUE';
      case 'P2006':
        return 'INVALID_VALUE_PROVIDED';
      case 'P2007':
        return 'DATA_VALIDATION_ERROR';
      case 'P2008':
        return 'QUERY_PARSING_ERROR';
      case 'P2009':
        return 'QUERY_VALIDATION_ERROR';
      case 'P2010':
        return 'RAW_QUERY_FAILED';
      case 'P2011':
        return 'NULL_CONSTRAINT_VIOLATION';
      case 'P2012':
        return 'MISSING_REQUIRED_VALUE';
      case 'P2013':
        return 'MISSING_REQUIRED_ARGUMENT';
      case 'P2014':
        return 'RELATION_VIOLATION';
      case 'P2015':
        return 'RELATED_RECORD_NOT_FOUND';
      case 'P2016':
        return 'QUERY_INTERPRETATION_ERROR';
      case 'P2017':
        return 'RECORDS_NOT_CONNECTED';
      case 'P2018':
        return 'REQUIRED_CONNECTED_RECORDS_NOT_FOUND';
      case 'P2019':
        return 'INPUT_ERROR';
      case 'P2020':
        return 'VALUE_OUT_OF_RANGE';
      case 'P2021':
        return 'TABLE_NOT_FOUND';
      case 'P2022':
        return 'COLUMN_NOT_FOUND';
      case 'P2023':
        return 'INCONSISTENT_COLUMN_DATA';
      case 'P2024':
        return 'CONNECTION_POOL_TIMEOUT';
      case 'P2025':
        return 'OPERATION_FAILED';
      case 'P2026':
        return 'UNSUPPORTED_FEATURE';
      case 'P2027':
        return 'MULTIPLE_ERRORS';
      default:
        return `PRISMA_ERROR_${error.code}`;
    }
  }

  private getErrorSeverity(error: any): string {
    if (!error.code) return 'HIGH';

    // Connection and infrastructure errors are critical
    const criticalErrors = ['P1000', 'P1001', 'P1002', 'P1003', 'P1008', 'P1010', 'P1011', 'P2024'];
    if (criticalErrors.includes(error.code)) return 'CRITICAL';

    // Data integrity errors are high priority
    const highPriorityErrors = ['P2002', 'P2003', 'P2004', 'P2011', 'P2014'];
    if (highPriorityErrors.includes(error.code)) return 'HIGH';

    // Validation and input errors are medium priority
    const mediumPriorityErrors = ['P2000', 'P2005', 'P2006', 'P2007', 'P2012', 'P2013', 'P2019'];
    if (mediumPriorityErrors.includes(error.code)) return 'MEDIUM';

    return 'LOW';
  }

  private isConnectionError(error: any): boolean {
    if (!error.code) return false;
    const connectionErrors = [
      'P1000',
      'P1001',
      'P1002',
      'P1003',
      'P1008',
      'P1010',
      'P1011',
      'P2024',
    ];
    return connectionErrors.includes(error.code);
  }
}
