/**
 * Utility functions for sanitizing message bodies in SQS operations
 * Provides consistent message sanitization across producers and consumers
 */

/**
 * Default sensitive field patterns that should be redacted in logs
 * These patterns are checked case-insensitively against object keys
 */
export const DEFAULT_SENSITIVE_FIELDS = [
  'password',
  'token',
  'secret',
  'key',
  'authorization',
  'credential',
  'auth',
  'bearer',
  'apiKey',
  'accessToken',
  'refreshToken',
  'sessionToken',
  'privateKey',
  'publicKey',
  'signature',
  'hash',
  'salt',
] as const;

/**
 * Default maximum size for message body logging (in characters)
 * Messages larger than this will be truncated with metadata
 */
export const DEFAULT_MAX_MESSAGE_SIZE = 1000;

/**
 * Sanitizes a message body for safe logging by redacting sensitive fields
 * and truncating large payloads. When truncation occurs, returns a summary
 * object with metadata instead of the full payload.
 *
 * @param messageBody - The message body to sanitize (supports Buffer, string, or object)
 * @param sensitiveFields - Array of sensitive field patterns to redact (optional)
 * @param maxSize - Maximum size before truncation (optional)
 * @returns Sanitized message body safe for logging, or truncation summary if too large
 */
export function sanitizeMessageBody(
  messageBody: any,
  sensitiveFields: readonly string[] = DEFAULT_SENSITIVE_FIELDS,
  maxSize: number = DEFAULT_MAX_MESSAGE_SIZE,
): any {
  if (!messageBody) return messageBody;

  try {
    // Handle Buffer objects by converting to string first
    let processedMessageBody = messageBody;
    if (Buffer.isBuffer(messageBody)) {
      const stringData = messageBody.toString('utf8');
      // Try to parse as JSON if it looks like JSON, otherwise keep as string
      try {
        processedMessageBody = JSON.parse(stringData);
      } catch {
        processedMessageBody = stringData;
      }
    }

    // Deep clone to avoid mutating original object
    const sanitized = JSON.parse(JSON.stringify(processedMessageBody));

    // Redact sensitive fields
    redactSensitiveFields(sanitized, sensitiveFields);

    // Check if truncation is needed
    const bodyString = JSON.stringify(sanitized);
    if (bodyString.length > maxSize) {
      // Create a meaningful summary instead of including the full object
      const summary = typeof sanitized === 'object' && sanitized !== null
        ? `Object with keys: [${Object.keys(sanitized).join(', ')}]`
        : `${typeof sanitized} payload`;

      return {
        _summary: summary,
        _truncated: true,
        _originalSize: bodyString.length,
        _maxSize: maxSize,
        _note: 'Message body truncated for logging',
      };
    }

    return sanitized;
  } catch (error) {
    return '[UNPARSEABLE_MESSAGE_BODY]';
  }
}

/**
 * Recursively redacts sensitive fields in an object by replacing their values with '[REDACTED]'
 *
 * @param obj - The object to process
 * @param sensitiveFields - Array of sensitive field patterns to redact
 */
export function redactSensitiveFields(
  obj: any,
  sensitiveFields: readonly string[] = DEFAULT_SENSITIVE_FIELDS,
): void {
  if (!obj || typeof obj !== 'object') return;

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const lowerKey = key.toLowerCase();

      // Check if this key matches any sensitive field pattern
      if (sensitiveFields.some(field => lowerKey.includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        // Recursively process nested objects and arrays
        redactSensitiveFields(obj[key], sensitiveFields);
      }
    }
  }
}

/**
 * Sanitizes an array of batch messages for logging
 * Used specifically for SQS batch operations
 *
 * @param messages - Array of messages to sanitize
 * @param maxBatchSize - Maximum size before batch truncation (optional)
 * @returns Sanitized batch messages safe for logging
 */
export function sanitizeBatchMessages(messages: any[], maxBatchSize: number = 2000): any {
  if (!messages || messages.length === 0) return messages;

  try {
    const sanitizedMessages = messages.map((message, index) => {
      if (message.MessageBody) {
        try {
          const parsedBody = JSON.parse(message.MessageBody);
          return {
            Id: message.Id,
            MessageBody: sanitizeMessageBody(parsedBody),
            _index: index,
          };
        } catch {
          return {
            Id: message.Id,
            MessageBody: '[UNPARSEABLE_MESSAGE_BODY]',
            _index: index,
          };
        }
      }
      return {
        Id: message.Id,
        MessageBody: message.MessageBody,
        _index: index,
      };
    });

    // Check if batch truncation is needed
    const batchString = JSON.stringify(sanitizedMessages);
    if (batchString.length > maxBatchSize) {
      return {
        _batchSummary: `${messages.length} messages`,
        _truncated: true,
        _originalSize: batchString.length,
        _maxBatchSize: maxBatchSize,
        _note: 'Batch messages truncated for logging',
        _sampleMessages: sanitizedMessages.slice(0, 2), // Show first 2 messages as sample
      };
    }

    return sanitizedMessages;
  } catch (error) {
    return '[UNPARSEABLE_BATCH_MESSAGES]';
  }
}
