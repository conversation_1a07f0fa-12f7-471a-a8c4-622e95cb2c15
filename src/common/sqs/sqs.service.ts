import {
  SendMessageBatchCommand,
  SendMessageBatchCommandOutput,
  SendMessageBatchRequestEntry,
  SendMessageCommand,
  SendMessageCommandOutput,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { CommunicationChannel } from '@common/enums';
import { logger } from '@edutalent/commons-sdk';
import { Injectable } from '@nestjs/common';
import { Consumer } from 'sqs-consumer';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class SQSService {
  private readonly sqsClient: SQSClient;
  private readonly importItemQueuePrefix: string;
  private readonly sqsQueueBaseUrl: string;
  private readonly importItemQueueSufix: string;

  constructor() {
    this.sqsClient = new SQSClient({
      region: process.env.AWS_REGION,
    });
    this.importItemQueuePrefix = process.env.IMPORT_ITEM_QUEUE_PREFIX;
    this.sqsQueueBaseUrl = process.env.SQS_QUEUE_BASE_URL;
    this.importItemQueueSufix = process.env.IMPORT_ITEM_QUEUE_SUFIX;
  }

  public async produce(
    queueUrl: string,
    messageBody: any,
  ): Promise<SendMessageCommandOutput | void> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    try {
      const params = { QueueUrl: queueUrl, MessageBody: JSON.stringify(messageBody) };
      const response = await this.sqsClient.send(new SendMessageCommand(params));

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('SQS message production failed', {
        traceId,
        queueUrl: queueUrl,
        duration: `${duration}ms`,
        error: JSON.stringify(error),
        operation: 'produce_message_error',
      });

      throw error;
    }
  }

  public async produceBatch(
    queueUrl: string,
    messages: SendMessageBatchRequestEntry[],
  ): Promise<SendMessageBatchCommandOutput | void> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    const params = {
      QueueUrl: queueUrl,
      Entries: messages,
    };

    try {
      const response = await this.sqsClient.send(new SendMessageBatchCommand(params));
      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('SQS batch message production failed', {
        traceId,
        queueUrl: queueUrl,
        batchSize: messages.length,
        duration: `${duration}ms`,
        error: JSON.stringify(error),
        layer: 'SQS_PRODUCER',
        operation: 'produce_batch_error',
      });

      throw error;
    }
  }

  public createConsumer(queueUrl: string, batchSize: number, handler): void {
    logger.info('Creating consumer for queue: ', queueUrl);

    try {
      // Add random polling wait time to prevent multiple consumers from polling simultaneously
      const randomPollingWaitTime = Math.floor(Math.random() * 3000) + 1000; // 1-4 seconds

      const consumer = Consumer.create({
        queueUrl: queueUrl,
        batchSize: batchSize,
        handleMessageBatch: handler,
        pollingWaitTimeMs: randomPollingWaitTime, // Random delay between polls
        waitTimeSeconds: 20, // Long polling to reduce API calls
      });

      consumer.start();
      logger.info(
        `Consumer started for queue: ${queueUrl} with ${randomPollingWaitTime}ms polling interval`,
      );
    } catch (error) {
      logger.error('SQS consumer creation failed', {
        queueUrl: queueUrl,
        batchSize,
        error: JSON.stringify(error),
        layer: 'SQS_CONSUMER_CREATION',
        operation: 'create_consumer_error',
      });
    }
  }

  public getQueueByTypeAndSegment(type: string, segment: string): string {
    return `PORTFOLIO_${type}_${segment}_QUEUE_URL`.toUpperCase();
  }

  public getImportItemQueueByCustomer(customerId: string) {
    const traceId = CorrelationContextService.getTraceId();
    logger.info('Getting import item queue for customer', {
      traceId,
      customerId,
      operation: 'getImportItemQueueByCustomer',
      layer: 'SQS_PRODUCER',
    });

    return `${this.sqsQueueBaseUrl}${this.importItemQueuePrefix}${customerId}${this.importItemQueueSufix}`;
  }

  public getOutgoingQueueNameByChannel(_communicationChannel: CommunicationChannel): string {
    return `OUTGOING_MESSAGE_QUEUE_URL`.toUpperCase();
  }
}
