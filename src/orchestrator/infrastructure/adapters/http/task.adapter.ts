import { Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { ExecuteTask } from '@orchestrator/misc/interfaces/in/execute-task';
import { TaskPort } from '@orchestrator/infrastructure/ports/http/task.port';
import { ExecuteSimplifiedTask } from '@orchestrator/misc/interfaces/in/execute-simplified-task';
import { logger } from '@edutalent/commons-sdk';
import { handleHttpError } from '@common/utils/handle-http-error';

@Injectable()
export class TaskAdapter implements TaskPort {
  private intelligenceServiceUrl: string;

  constructor(private httpService: HttpService) {
    this.intelligenceServiceUrl = process.env.INTELLIGENCE_SERVICE_URL.toString();
  }

  async executeTask(data: ExecuteTask): Promise<any> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/tasks/execute`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response: AxiosResponse<any> = await lastValueFrom(
        this.httpService.post(url, data, { headers, timeout: 500000 }),
      );
      return response.data;
    } catch (error) {
      handleHttpError(error, 'Task-adapter');
    }
  }

  async executeSimplifiedTask(data: ExecuteSimplifiedTask): Promise<any> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/tasks/execute-simplified`;

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response: AxiosResponse<any> = await lastValueFrom(
        this.httpService.post(url, data, { headers, timeout: 60000 }),
      );
      return response.data;
    } catch (error) {
      logger.error(
        `Error ExecuteSimplifiedTask: Data: ${JSON.stringify(data)} Error: ${JSON.stringify(
          error,
        )}`,
      );
      handleHttpError(error, 'Task-adapter');
    }
  }

  async getTasksAndAgentsBackstoryVariables(taskId: string): Promise<string[]> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/tasks/${taskId}/variables`;
      const response: AxiosResponse<any> = await lastValueFrom(this.httpService.get(url));
      return response.data.data;
    } catch (error) {
      handleHttpError(error, 'Task-adapter');
    }
  }
}
