import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { IntegrationHubController } from '@integration-hub/application/controllers/integration-hub.controller';
import { IntegrationUseCase } from '@integration-hub/application/use-cases/integration.use-case';
import { InfraBusinessBaseAdapter } from '@integration-hub/infrastructure/adapters/http/business-base.adapter';
import { IntegrationStrategyFactory } from '@integration-hub/domain/factories/integration-strategy.factory';
import { IntegrationStrategyManager } from '@integration-hub/domain/services/integration-strategy.manager';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { CustomerChannelIntegrationDataDefinitionAdapter } from '@common/auth/db/adapters/customer-channel-integration-data-definition.adapter';
import { DynamoService } from '@common/dynamo/dynamo.service';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 180000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [
    httpModule,
  ],
  providers: [
    IntegrationUseCase,
    IntegrationStrategyManager,
    IntegrationStrategyFactory,
    RecuperaIntegrationStrategy,
    DynamoService,
    {
      provide: 'BusinessBasePort',
      useClass: InfraBusinessBaseAdapter,
    },
    {
      provide: 'CustomerChannelIntegrationDataDefinitionPort',
      useClass: CustomerChannelIntegrationDataDefinitionAdapter,
    },
  ],
  controllers: [
    IntegrationHubController,
  ],
})
export class IntegrationHubModule { }
