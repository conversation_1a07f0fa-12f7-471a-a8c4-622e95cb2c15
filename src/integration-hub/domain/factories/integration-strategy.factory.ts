import { Injectable } from '@nestjs/common';
import { IntegrationStrategy } from '@integration-hub/domain/interfaces/integration-strategy.interface';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';

@Injectable()
export class IntegrationStrategyFactory {
  private readonly strategies: Map<string, IntegrationStrategy> = new Map();

  constructor(
    private readonly recuperaStrategy: RecuperaIntegrationStrategy,
  ) {
    this.registerStrategies();
  }

  private registerStrategies(): void {
    this.registerStrategy(this.recuperaStrategy);
  }

  private registerStrategy(strategy: IntegrationStrategy): void {
    const strategyName = strategy.getStrategyName().toLowerCase();
    this.strategies.set(strategyName, strategy);
  }

  createStrategy(context: IntegrationContext): IntegrationStrategy {
    const providerName = context.getProviderName();
    
    if (!providerName) {
      throw new BusinessException(
        'IntegrationStrategyFactory',
        'Provider name is required to determine integration strategy',
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }

    for (const strategy of this.strategies.values()) {
      if (strategy.canHandle(context)) {
        return strategy;
      }
    }

    // If no strategy can handle the context, throw an exception
    throw new BusinessException(
      'IntegrationStrategyFactory',
      `No integration strategy found for provider: ${providerName}`,
      BusinessExceptionStatus.ITEM_NOT_FOUND,
    );
  }
}
