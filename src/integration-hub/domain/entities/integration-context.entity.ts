import { IsUUID, IsString, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class IntegrationContext {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsString()
  @IsNotEmpty()
  readonly currentStatus: string;

  @ValidateNested()
  @IsNotEmpty()
  readonly integrationConfig: Record<string, any>;

  @IsOptional()
  readonly metadata?: Record<string, any>;

  constructor(
    customerId: string,
    portfolioItemId: string,
    portfolioId: string,
    workflowId: string,
    currentStatus: string,
    integrationConfig: Record<string, any>,
    metadata?: Record<string, any>
  ) {
    this.customerId = customerId;
    this.portfolioItemId = portfolioItemId;
    this.portfolioId = portfolioId;
    this.workflowId = workflowId;
    this.currentStatus = currentStatus;
    this.integrationConfig = integrationConfig;
    this.metadata = metadata;
  }

  getProviderName(): string {
    return this.integrationConfig.providerName;
  }

}
