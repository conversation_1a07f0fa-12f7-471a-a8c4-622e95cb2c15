import { IsBoolean, IsString, IsOptional, IsDate, IsNotEmpty } from 'class-validator';

export class IntegrationResult {
  @IsBoolean()
  @IsNotEmpty()
  readonly success: boolean;

  @IsString()
  @IsNotEmpty()
  readonly strategyName: string;

  @IsString()
  @IsOptional()
  readonly message?: string;

  @IsOptional()
  readonly data?: any;

  @IsOptional()
  readonly error?: string;

  @IsDate()
  @IsNotEmpty()
  readonly executedAt: Date;

  @IsOptional()
  readonly metadata?: Record<string, any>;

  constructor(
    success: boolean,
    strategyName: string,
    message?: string,
    data?: any,
    error?: string,
    metadata?: Record<string, any>
  ) {
    this.success = success;
    this.strategyName = strategyName;
    this.message = message;
    this.data = data;
    this.error = error;
    this.executedAt = new Date();
    this.metadata = metadata;
  }

  static success(
    strategyName: string,
    message?: string,
    data?: any,
    metadata?: Record<string, any>
  ): IntegrationResult {
    return new IntegrationResult(true, strategyName, message, data, undefined, metadata);
  }

  static failure(
    strategyName: string,
    error: string,
    message?: string,
    metadata?: Record<string, any>
  ): IntegrationResult {
    return new IntegrationResult(false, strategyName, message, undefined, error, metadata);
  }

  hasData(): boolean {
    return this.data !== undefined && this.data !== null;
  }

}
