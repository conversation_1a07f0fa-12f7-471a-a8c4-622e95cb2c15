import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { IntegrationStrategy } from '@integration-hub/domain/interfaces/integration-strategy.interface';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationResult } from '@integration-hub/domain/entities/integration-result.entity';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { MessageType, RoleType } from '../../../common/enums';
import { randomUUID } from 'crypto';

@Injectable()
export class RecuperaIntegrationStrategy implements IntegrationStrategy {

  constructor(
    private readonly httpService: HttpService,
    @Inject('BusinessBasePort')
    private readonly businessBaseAdapter: BusinessBasePort,
  ) {}
  private readonly strategyName = 'Recupera';
  private authToken: string;

  async execute(context: IntegrationContext): Promise<IntegrationResult> {
    logger.info(`Executing ${this.strategyName} strategy`, {
      customerId: context.customerId,
      portfolioItemId: context.portfolioItemId,
      currentStatus: context.currentStatus,
      providerName: context.getProviderName(),
    });

    try {
      const portfolioItem = await this.businessBaseAdapter.getPortfolioItem(context.portfolioItemId);
      if (!portfolioItem || !portfolioItem.customData) {
        throw new Error('Portfolio item custom data not found');
      }

      const integrationData = await this.performRecuperaIntegration(context, portfolioItem.customData);
      
      logger.info(`${this.strategyName} strategy executed successfully`, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });

      // Convert base64 to PDF file and send to business base
      const pdfFile = this.base64ToPdfFile(integrationData);
      await this.businessBaseAdapter.sendDirectMessage(context.portfolioItemId, {
        message: 'Boleto para pagamento.',
        messageType: MessageType.PDF,
        roleType: RoleType.ASSISTANT,
      }, pdfFile);

      return IntegrationResult.success(
        this.strategyName,
        'Recupera integration completed successfully',
        {
          executionTime: new Date().toISOString(),
          portfolioItemId: context.portfolioItemId,
        }
      );
    } catch (error) {
      this.logError(`${this.strategyName} strategy execution failed`, error, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });

      throw error;
    }
  }

  base64ToPdfFile(base64: string): Express.Multer.File {
    if (!base64?.trim()) {
      throw new Error('Base64 data is required');
    }

    // Remove data URL prefix se existir
    const data = base64.replace(/^data:application\/pdf;base64,/, '');
    const buffer = Buffer.from(data, 'base64');

    // Validação básica de PDF
    if (buffer.length < 4 || buffer.subarray(0, 4).toString() !== '%PDF') {
      throw new Error('Invalid PDF data');
    }

    const filename = `${randomUUID()}.pdf`;

    return {
      fieldname: 'file',
      originalname: filename,
      encoding: '7bit',
      mimetype: 'application/pdf',
      size: buffer.length,
      buffer,
      // Campos obrigatórios mas não utilizados
      destination: '',
      filename,
      path: '',
      stream: undefined as any,
    };
  }

  getStrategyName(): string {
    return this.strategyName;
  }

  canHandle(context: IntegrationContext): boolean {
    const providerName = context.getProviderName();
    return providerName && providerName.toLowerCase() === this.strategyName.toLowerCase();
  }

  private async withRetry<T>(
    operation: () => Promise<T>,
    context: IntegrationContext,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Se é erro de token, renova e tenta novamente
        if (this.isTokenError(error) && attempt < maxRetries) {
          this.logError('Token invalid/expired, renewing token', error, {
            customerId: context.customerId,
            portfolioItemId: context.portfolioItemId,
            attempt
          });
          await this.getAuthToken(context);
          continue;
        }

        if (attempt === maxRetries) break;

        this.logError(`Attempt ${attempt} failed, retrying`, error, {
          customerId: context.customerId,
          portfolioItemId: context.portfolioItemId
        });

        // Backoff exponencial
        await this.delay(1000 * Math.pow(2, attempt - 1));
      }
    }

    throw lastError;
  }

  private isTokenError(error: any): boolean {
    return error.response?.status === 401 &&
           error.response?.headers?.['www-authenticate']?.includes('invalid_token');
  }

  private isSecondCopyNotFound(error: any): boolean {
    return error.response?.status === 400 &&
           error.response?.data?.mensagemRetorno?.includes('Dívida de segunda via original legado não foi encontrada');
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private logError(message: string, error: Error, context?: any): void {
    logger.error(message, {
      error: error.message,
      stack: error.stack,
      ...context
    });
  }

  private async performRecuperaIntegration(context: IntegrationContext, customData: any): Promise<string> {
    try {
      // Tenta segunda via primeiro
      return await this.getBankSlipPdfBase64(context, customData, 'segunda-via');
    } catch (error) {
      // Se não encontrar segunda via, tenta original
      if (this.isSecondCopyNotFound(error)) {
        logger.info('Segunda via não encontrada, tentando boleto original', {
          customerId: context.customerId,
          portfolioItemId: context.portfolioItemId,
        });
        return await this.getBankSlipPdfBase64(context, customData, 'original');
      }

      this.logError('Failed to perform recupera integration', error, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });
      throw error;
    }
  }

  private async getBankSlipPdfBase64(
    context: IntegrationContext,
    customData: any,
    endpoint: 'original' | 'segunda-via' = 'segunda-via'
  ): Promise<string> {
    const urls = {
      'original': '/api/boleto/original/pdf',
      'segunda-via': '/api/boleto/segunda-via/original/pdf'
    };

    return this.withRetry(async () => {
      await this.ensureValidToken(context);

      const response = await lastValueFrom(
        this.httpService.post(
          `https://cobrancaapi-matrixenergia.recupera.com.br${urls[endpoint]}`,
          this.buildRequestBody(customData),
          { headers: this.getHeaders() }
        )
      );

      return response.data.boletoPDF;
    }, context);
  }

  private async ensureValidToken(context: IntegrationContext): Promise<void> {
    if (!this.authToken) {
      await this.getAuthToken(context);
    }
  }

  private buildRequestBody(customData: any): any {
    return {
      chaveCliente: {
        codigoCredor: customData['CODIGO_DO_CREDOR'],
        codigoCliente: customData['CODIGO_DO_CLIENTE'],
      },
      dataPagamento: customData['DATA_PAGAMENTO'],
      divida: [
        {
          codigoProduto: customData['CODIGO_DO_PRODUTO'],
          codigoContrato: customData['CODIGO_DO_CONTRATO'],
          prestacoes: [
            {
              numeroPrestacao: customData['NUMERO_DA_PRESTACAO'] || '',
              dataVencimento: customData['DATA_VENCIMENTO'],
              idPrestacao: customData['ID_DA_PRESTACAO'],
            },
          ],
        },
      ],
    };
  }

  private getHeaders(): any {
    return {
      accept: 'text/plain',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.authToken}`,
    };
  }

  private async getAuthToken(context: IntegrationContext): Promise<void> {
    try {
      const url = 'https://cobrancaapi-matrixenergia.recupera.com.br/api/autenticacao/autenticar';
      const headers = {
        accept: 'text/plain',
        'Content-Type': 'application/json',
      };

      const body = {
        usuario: context.integrationConfig.credentials.usuario,
        senha: context.integrationConfig.credentials.senha,
        empresa: context.integrationConfig.credentials.empresa,
        chaveAtivacao: context.integrationConfig.credentials.chaveAtivacao,
      };

      const response = await lastValueFrom(this.httpService.post(url, body, { headers }));
      this.authToken = response.data.access_token;
    } catch (error) {
      this.logError('Failed to get auth token', error, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });
      throw error;
    }
  }
}
