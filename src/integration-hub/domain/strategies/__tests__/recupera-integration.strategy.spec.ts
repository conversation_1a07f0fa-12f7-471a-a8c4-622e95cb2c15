import { Test, TestingModule } from '@nestjs/testing';
import { RecuperaIntegrationStrategy } from '../recupera-integration.strategy';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationConfig } from '@integration-hub/infrastructure/dto/out/customer-preferences-response.dto';

describe('RecuperaIntegrationStrategy', () => {
  let strategy: RecuperaIntegrationStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RecuperaIntegrationStrategy],
    }).compile();

    strategy = module.get<RecuperaIntegrationStrategy>(RecuperaIntegrationStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should return correct strategy name', () => {
    expect(strategy.getStrategyName()).toBe('Recupera');
  });

  it('should handle context with Recupera provider', () => {
    const integrationConfig = new IntegrationConfig();
    integrationConfig.providerName = 'Recupera';

    const context = new IntegrationContext(
      'customer-id',
      'portfolio-item-id',
      'portfolio-id',
      'workflow-id',
      'FINISHED',
      integrationConfig,
    );

    expect(strategy.canHandle(context)).toBe(true);
  });

  it('should not handle context with different provider', () => {
    const integrationConfig = new IntegrationConfig();
    integrationConfig.providerName = 'OtherProvider';

    const context = new IntegrationContext(
      'customer-id',
      'portfolio-item-id',
      'portfolio-id',
      'workflow-id',
      'FINISHED',
      integrationConfig,
    );

    expect(strategy.canHandle(context)).toBe(false);
  });

  it('should execute successfully', async () => {
    const integrationConfig = new IntegrationConfig();
    integrationConfig.providerName = 'Recupera';

    const context = new IntegrationContext(
      'customer-id',
      'portfolio-item-id',
      'portfolio-id',
      'workflow-id',
      'FINISHED',
      integrationConfig,
    );

    const result = await strategy.execute(context);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.strategyName).toBe('Recupera');
    expect(result.message).toBe('Recupera integration completed successfully');
    expect(result.data).toBeDefined();
    expect(result.data.portfolioItemId).toBe('portfolio-item-id');
    expect(result.data.customerId).toBe('customer-id');
  });
});
