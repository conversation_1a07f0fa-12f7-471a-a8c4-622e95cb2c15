import { Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { IntegrationStrategyFactory } from '@integration-hub/domain/factories/integration-strategy.factory';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationResult } from '@integration-hub/domain/entities/integration-result.entity';
import { IntegrationStrategy } from '@integration-hub/domain/interfaces/integration-strategy.interface';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';
import { AxiosError } from 'axios';

@Injectable()
export class IntegrationStrategyManager {
  constructor(
    private readonly strategyFactory: IntegrationStrategyFactory,
  ) {}

  async executeStrategy(context: IntegrationContext): Promise<IntegrationResult> {
    logger.info('Starting integration strategy execution', {
      customerId: context.customerId,
      portfolioItemId: context.portfolioItemId,
      providerName: context.getProviderName(),
    });

    try {
      const strategy = this.strategyFactory.createStrategy(context);
      
      logger.info(`Selected strategy: ${strategy.getStrategyName()}`, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });

      const result = await strategy.execute(context);

      logger.info('Integration strategy execution completed', {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        strategyName: result.strategyName,
        success: result.success,
      });

      return result;
    } catch (error) {
      logger.error('Integration strategy execution failed', {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        error: error.message,
      });

      if (error instanceof BusinessException) {
        throw error;
      }

      if (error instanceof AxiosError) {
        throw new BusinessException(
          'IntegrationStrategyManager',
          `Failed to execute strategy for provider ${context.getProviderName()}: ${JSON.stringify(error.response.data)}`,
          BusinessExceptionStatus.GENERAL_ERROR,
        );
      }

      throw new BusinessException(
        'IntegrationStrategyManager',
        `Failed to execute integration strategy: ${error.message}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }
  }
}
