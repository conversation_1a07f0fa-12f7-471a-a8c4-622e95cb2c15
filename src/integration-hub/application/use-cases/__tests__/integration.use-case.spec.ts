import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationUseCase } from '../integration.use-case';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { IntegrationStrategyManager } from '@integration-hub/domain/services/integration-strategy.manager';
import { IntegrationStrategyFactory } from '@integration-hub/domain/factories/integration-strategy.factory';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { BusinessException } from '@common/exception/types/BusinessException';
import { RecordStatus } from '@common/enums';
import { PortfolioPreferences } from '@business-base/domain/entities/customer-preferences.entity';

describe('IntegrationUseCase', () => {
  let useCase: IntegrationUseCase;
  let businessBasePort: jest.Mocked<BusinessBasePort>;
  let strategyManager: IntegrationStrategyManager;

  beforeEach(async () => {
    const mockBusinessBasePort = {
      getCustomerPreferences: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationUseCase,
        IntegrationStrategyManager,
        IntegrationStrategyFactory,
        RecuperaIntegrationStrategy,
        {
          provide: 'BusinessBasePort',
          useValue: mockBusinessBasePort,
        },
      ],
    }).compile();

    useCase = module.get<IntegrationUseCase>(IntegrationUseCase);
    businessBasePort = module.get('BusinessBasePort');
    strategyManager = module.get<IntegrationStrategyManager>(IntegrationStrategyManager);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should execute integration strategy successfully', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: undefined,
    };

    const integrationConfig = {
      providerName: 'Recupera',
      credentials: {
        usuario: 'usuario',
        senha: 'senha',
        empresa: 'empresa',
        chaveAtivacao: 'chaveAtivacao',
      },
    };

    const portfolioPreferences = new PortfolioPreferences();
    portfolioPreferences.integrationConfig = integrationConfig;

    const customerPreferences: CustomerPreferencesResponseDTO = {
      customerId: 'customer-id',
      portfolio: portfolioPreferences,
      status: RecordStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    businessBasePort.getCustomerPreferences.mockResolvedValue(customerPreferences);

    const result = await useCase.executeIntegrationStrategy(request);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.strategyName).toBe('Recupera');
    expect(businessBasePort.getCustomerPreferences).toHaveBeenCalledWith('customer-id');
  });

  it('should throw exception when integration config is missing', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
    };

    const portfolioPreferences = new PortfolioPreferences();
    // No integration config

    const customerPreferences: CustomerPreferencesResponseDTO = {
      customerId: 'customer-id',
      portfolio: portfolioPreferences,
      status: RecordStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    businessBasePort.getCustomerPreferences.mockResolvedValue(customerPreferences);

    await expect(useCase.executeIntegrationStrategy(request)).rejects.toThrow(BusinessException);
  });

  it('should throw exception when portfolio is missing', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
    };

    const customerPreferences: CustomerPreferencesResponseDTO = {
      customerId: 'customer-id',
      portfolio: undefined,
      status: RecordStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    businessBasePort.getCustomerPreferences.mockResolvedValue(customerPreferences);

    await expect(useCase.executeIntegrationStrategy(request)).rejects.toThrow(BusinessException);
  });
});
