import { applyDecorators } from "@nestjs/common";
import { ApiBody, ApiConsumes, ApiOperation, ApiProduces, ApiResponse } from "@nestjs/swagger";
import { PortfolioItemUpdatedRequestDTO } from "@integration-hub/application/dto/in/portfolio-item-updated-request.dto";

export class IntegrationOperations {
  static executeIntegration() {
    return applyDecorators(
      ApiOperation({
        summary: 'Execute integration strategy',
        description: 'This endpoint execute an integration strategy based on customer preferences',
      }),
      ApiProduces('application/json'),
      ApiConsumes('application/json'),
      ApiBody({
        type: PortfolioItemUpdatedRequestDTO,
        description: 'Portfolio item updated request',
        examples: {
          example: {
            summary: 'Portfolio item updated request',
            value: {
              customerId: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
              portfolioItemId: '6115d2cc-95c5-4cbc-b7e8-133680001dba',
              portfolioId: '30b0e1fb-64c7-4519-93aa-4eb5dd33087e',
              workflowId: '43e3ceed-58da-4ff2-bddf-67cb79d4433f',
              currentStatus: 'FINISHED',
            },
          },
        },
      }),
      ApiResponse({
        status: 200,
        description: 'Integration executed successfully',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            data: { type: 'object' },
          },
        },
      }),
      ApiResponse({
        status: 400,
        description: 'Bad Request',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            message: { type: 'string' },
          },
        },
      }),
      ApiResponse({
        status: 404,
        description: 'Not Found',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            message: { type: 'string' },
          },
        },
      }),
      ApiResponse({
        status: 500,
        description: 'Internal Server Error',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            message: { type: 'string' },
          },
        },
      }),
    );
  }
}