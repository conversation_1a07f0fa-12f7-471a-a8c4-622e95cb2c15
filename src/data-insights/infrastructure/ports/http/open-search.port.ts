export interface OpenSearchPort {
  indexDocument(index: string, document: Record<string, any>): Promise<any>;

  searchSimilar(
    index: string,
    size: number,
    like: string,
    fields: string[],
    additionalFilters?: Record<string, any>,
  ): Promise<any[]>;

  searchSimilar2(
    index: string,
    size: number,
    like: string,
    fields: string[],
    additionalFilters?: Record<string, any>,
  ): Promise<any[]>;
}
