import { OpenSearchPort } from '@data-insights/infrastructure/ports/http/open-search.port';
import { logger } from '@edutalent/commons-sdk';
import { Injectable } from '@nestjs/common';
import { Client } from '@opensearch-project/opensearch';
import { AwsSigv4Signer } from '@opensearch-project/opensearch/aws';
import { defaultProvider } from '@aws-sdk/credential-provider-node';

@Injectable()
export class OpenSearchAdapter implements OpenSearchPort {
  private client: Client;

  constructor() {
    const opensearchNode = process.env.OPENSEARCH_NODE || 'http://localhost:9200';

    if (!opensearchNode.includes('localhost')) {
      this.client = new Client({
        ...AwsSigv4Signer({
          region: 'us-east-1',
          service: 'es', // 'aoss' for OpenSearch Serverless
          getCredentials: () => {
            const credentialsProvider = defaultProvider();
            return credentialsProvider();
          },
        }),
        node: opensearchNode,
      });
    } else {
      this.client = new Client({ node: opensearchNode });
    }
  }

  private async ensureIndex(collection: string) {
    try {
      const exists = await this.client.indices.exists({ index: collection });
      if (!exists.body) {
        await this.client.indices.create({
          index: collection,
          body: {
            mappings: {
              properties: {},
            },
          },
        });
      }
    } catch (error) {
      logger.error(
        `OpenSearchAdapter: error ensuring index ${collection}: ${JSON.stringify(error)}`,
      );
      throw error;
    }
  }

  async indexDocument(index: string, document: Record<string, any>): Promise<any> {
    await this.ensureIndex(index);

    try {
      const response = await this.client.index({
        index: index,
        body: document,
      });

      await this.client.indices.refresh({ index: index });

      return response.body;
    } catch (error) {
      logger.error(`OpenSearchAdapter: error indexing document ${index}: ${JSON.stringify(error)}`);
      throw error;
    }
  }

  async searchSimilar(
    index: string,
    size: number,
    like: string,
    fields: string[],
    filters?: Record<string, any>,
  ): Promise<any[]> {
    try {
      const exists = await this.client.indices.exists({ index: index });
      if (!exists.body) {
        logger.error(`Index ${index} does not exist`);
        throw new Error(`Index ${index} does not exist`);
      }

      const response = await this.client.search({
        index: index,
        size: size,
        body: {
          query: {
            bool: {
              must: [
                {
                  more_like_this: {
                    fields,
                    like,
                    min_term_freq: 1,
                    min_doc_freq: 1,
                    max_query_terms: 12,
                  },
                },
              ],
              filter: this.buildFilters(filters),
            },
          },
        },
      });

      return response.body.hits.hits.map(hit => {
        return {
          ...hit._source,
          score: hit._score,
        };
      });
    } catch (error) {
      logger.error('Error searching similar documents:', error);
      throw error;
    }
  }

  async searchSimilar2(
    index: string,
    size: number,
    like: string,
    fields: string[],
    filters?: Record<string, any>,
  ): Promise<any[]> {
    try {
      const exists = await this.client.indices.exists({ index: index });
      if (!exists.body) {
        logger.error(`Index ${index} does not exist`);
        throw new Error(`Index ${index} does not exist`);
      }

      let response = await this.client.search({
        index: index,
        size: size,
        body: {
          query: {
            bool: {
              must: [
                {
                  more_like_this: {
                    fields,
                    like,
                    min_term_freq: 1,
                    min_doc_freq: 1,
                    max_query_terms: 25,
                    minimum_should_match: '1',
                  },
                },
              ],
              filter: this.buildFilters(filters),
            },
          },
        },
      });

      if (response.body.hits.hits.length === 0) {
        logger.info(`No results found for ${like}, trying with fuzziness`);

        response = await this.client.search({
          index: index,
          size: size,
          body: {
            query: {
              bool: {
                must: [{ multi_match: { query: like, fields, fuzziness: 'AUTO', operator: 'or' } }],
                filter: this.buildFilters(filters),
              },
            },
          },
        });
      }

      return response.body.hits.hits.map(hit => {
        return {
          ...hit._source,
          score: hit._score,
        };
      });
    } catch (error) {
      logger.error('Error searching similar documents:', error);
      throw error;
    }
  }

  private buildFilters(filters?: Record<string, any>) {
    if (!filters)
      return [
        {
          match_all: {},
        },
      ];

    return Object.entries(filters).map(([key, value]) => ({
      term: { [`${key}.keyword`]: value },
    }));
  }
}
