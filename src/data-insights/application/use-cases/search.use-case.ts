import { OpenSearchPort } from '@data-insights/infrastructure/ports/http/open-search.port';
import { Inject, Injectable } from '@nestjs/common';
import { SearchSimilarDto } from '@data-insights/application/dto/in/search-similar.dto';
import { IndexDocumentDto } from '@data-insights/application/dto/in/index-document.dto';
import { IndexDocumentResponseDto } from '@data-insights/application/dto/out/index-document-response.dto';

@Injectable()
export class SearchUseCase {
  constructor(@Inject('OpenSearchPort') private readonly openSearchAdapter: OpenSearchPort) {}

  async indexDocument(indexDocumentDto: IndexDocumentDto): Promise<IndexDocumentResponseDto> {
    const response = await this.openSearchAdapter.indexDocument(
      indexDocumentDto.index,
      indexDocumentDto.document,
    );

    return {
      index: response._index,
      id: response._id,
      version: response._version,
      result: response.result,
    };
  }

  async searchSimilar(searchSimilarDto: SearchSimilarDto): Promise<any[]> {
    return await this.openSearchAdapter.searchSimilar(
      searchSimilarDto.index,
      searchSimilarDto.size,
      searchSimilarDto.like,
      searchSimilarDto.fields,
      searchSimilarDto.filters,
    );
  }

  async searchSimilar2(searchSimilarDto: SearchSimilarDto): Promise<any[]> {
    return await this.openSearchAdapter.searchSimilar2(
      searchSimilarDto.index,
      searchSimilarDto.size,
      searchSimilarDto.like,
      searchSimilarDto.fields,
      searchSimilarDto.filters,
    );
  }
}
