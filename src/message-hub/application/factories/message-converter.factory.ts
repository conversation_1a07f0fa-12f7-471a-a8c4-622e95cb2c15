import { Injectable } from '@nestjs/common';
import { IMessageTransformerService } from '@message-hub/application/services/imessage-transformer.service';
import { WhatsAppSelfHostedMessageTransformerService } from '@message-hub/application/services/whatsapp-selfhosted-message-transformer.service';
import { CommunicationChannel } from '@common/enums';
import { SmsVonageMessageTransformerService } from '@message-hub/application/services/sms-vonage-selfhosted-message-transformer.service';
import { BlipColinaMessageTransformerService } from '@message-hub/application/services/blip-colina-message-transformer.service';
import { LovelaceMessageTransformerService } from '@message-hub/application/services/lovelace-message-transformer.service';
import { WhatsAppApiMessageTransformerService } from '@message-hub/application/services/whatsapp-api-message-transformer.service';

@Injectable()
export class MessageTransformerFactory {
  constructor() {}

  static getMessageTransformer(
    communicationChannel: CommunicationChannel,
  ): IMessageTransformerService<any> {
    switch (communicationChannel) {
      case CommunicationChannel.WHATSAPPSELFHOSTED:
        return new WhatsAppSelfHostedMessageTransformerService();
      case CommunicationChannel.SMS_VONAGE:
        return new SmsVonageMessageTransformerService();
      case CommunicationChannel.BLIP_COLINA:
        return new BlipColinaMessageTransformerService();
      case CommunicationChannel.LOVELACE:
        return new LovelaceMessageTransformerService();
      case CommunicationChannel.WHATSAPP_API:
        return new WhatsAppApiMessageTransformerService();
      default:
        throw new Error('Invalid service type');
    }
  }
}
