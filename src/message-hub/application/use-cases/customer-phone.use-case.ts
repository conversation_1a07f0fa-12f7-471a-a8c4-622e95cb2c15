import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import { CustomerPhoneDto } from '@message-hub/application/dto/in/customer-phone.dto';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { CustomerPhoneResponseDto } from '@message-hub/application/dto/out/customer-phone-response.dto';
import { plainToClass } from 'class-transformer';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { CustomerPhoneDestinationPort } from '@message-hub/infrastructure/ports/db/customer-phone-destination.port';
import { CommunicationChannel } from '@common/enums';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';

@Injectable()
export class CustomerPhoneUseCase {
  private readonly TARGET_TOTAL_WEIGHT = 100;

  constructor(
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
    @Inject('CustomerPhoneDestinationPort')
    private readonly customerPhoneDestinationAdapter: CustomerPhoneDestinationPort,
    @Inject(forwardRef(() => OutgoingMessageUseCase))
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
  ) {}

  async createCustomerPhone(
    customerId: string,
    customerPhoneDto: CustomerPhoneDto,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(`Creating customer phone for customer:   ${customerId}`);

    const customerPhoneEntity = new CustomerPhoneEntity(
      customerId,
      customerPhoneDto.phoneNumber,
      customerPhoneDto.communicationChannel,
      customerPhoneDto.incomingCron,
      customerPhoneDto.outgoingCron,
      customerPhoneDto.outgoingMaxDelay,
      customerPhoneDto.dailyLimit,
      customerPhoneDto.weight,
      customerPhoneDto.apiUrl,
    );

    const createdCustomerPhone = await this.customerPhoneAdapter.create(customerPhoneEntity);

    return this.getCustomerPhoneResponseDto(createdCustomerPhone);
  }

  async getCustomerPhoneByPhoneNumberAndCommunicationChannel(
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(`Fetching customer phone by phone number: ${phoneNumber}`);

    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByPhoneNumberAndCommunicationChannel(
        phoneNumber,
        communicationChannel,
      );

    if (!customerPhoneEntity) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `PhoneNumber of phone: ${phoneNumber} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return this.getCustomerPhoneResponseDto(customerPhoneEntity);
  }

  async getCustomerPhonesByCustomerId(customerId: string): Promise<CustomerPhoneResponseDto[]> {
    logger.info(`Fetching customer phones by customerId: ${customerId}`);

    const customerPhoneEntities = await this.customerPhoneAdapter.getAll({
      customerId,
    });

    const results = [];
    for (const entity of customerPhoneEntities) {
      const result = this.getCustomerPhoneResponseDto(entity);
      results.push(result);
    }
    return results;
  }

  async getCustomerPhoneByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<CustomerPhoneResponseDto[]> {
    logger.info(
      `Fetching customer phone by customerId: ${customerId} and phone number: ${phoneNumber}`,
    );

    const customerPhoneEntities = await this.customerPhoneAdapter.getAll({
      customerId,
      phoneNumber,
    });

    if (!customerPhoneEntities && customerPhoneEntities.length === 0) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `PhoneNumber of customer id: ${customerId} and ${phoneNumber} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return customerPhoneEntities.map(customerPhoneEntity =>
      this.getCustomerPhoneResponseDto(customerPhoneEntity),
    );
  }

  async getCustomerPhoneByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(
      `Fetching customer phone by customerId: ${customerId}, phone number: ${phoneNumber} and communication channel: ${communicationChannel}`,
    );

    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      );

    if (!customerPhoneEntity) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `PhoneNumber of customer id: ${customerId}, phone: ${phoneNumber} and communicationChannel: ${communicationChannel} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return this.getCustomerPhoneResponseDto(customerPhoneEntity);
  }

  async updatePhoneNumberApiUrl(
    customerId: string,
    phoneNumber: string,
    channel: CommunicationChannel,
    apiUrl: string,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(
      `Update customer phone api url by customerId: ${customerId} and phone number: ${phoneNumber}`,
    );

    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        channel,
      );
    const updatedCustomerPhone = {
      ...customerPhoneEntity,
      apiUrl: apiUrl,
    };

    await this.customerPhoneAdapter.update(updatedCustomerPhone);

    return this.getCustomerPhoneResponseDto(updatedCustomerPhone);
  }

  async deleteAllCustomerPhoneByCustomerId(customerId: string): Promise<void> {
    logger.info(`Deleting customer phones by customer id: ${customerId}`);

    await this.customerPhoneAdapter.deleteAllByCustomerId(customerId);
    await this.customerPhoneDestinationAdapter.deleteAllByCustomerId(customerId);
  }

  async deleteCustomerPhoneByCustomerIdAndPhone(
    customerId: string,
    phoneNumber: string,
  ): Promise<void> {
    logger.info(
      `Deleting customer phones by customer id: ${customerId} and phone number: ${phoneNumber}`,
    );

    try {
      const initialPhones = await this.customerPhoneAdapter.getAll({ customerId });
      logger.info(
        `Phone weights before deletion for customer ${customerId}: ` +
          JSON.stringify(
            initialPhones.map(phone => ({
              phoneNumber: phone.phoneNumber,
              weight: phone.weight,
            })),
          ),
      );

      const [phoneToDelete] = await this.customerPhoneAdapter.getAll({
        customerId,
        phoneNumber,
      });

      if (!phoneToDelete) {
        logger.error(`Phone number ${phoneNumber} for customer ${customerId} not found.`);
        throw new BusinessException(
          'CustomerPhoneUseCase',
          `Customer phone: ${phoneNumber} not found for customerId: ${customerId}`,
          BusinessExceptionStatus.ITEM_NOT_FOUND,
        );
      }

      const deletedPhoneWeight = Number(phoneToDelete.weight);

      await this.customerPhoneAdapter.deleteByCustomerIdAndPhoneNumber(customerId, phoneNumber);
      await this.customerPhoneDestinationAdapter.deleteAllByCustomerIdAndPhoneNumber(
        customerId,
        phoneNumber,
      );

      await this.rebalanceCustomerPhones(customerId, deletedPhoneWeight);
      this.outgoingMessageUseCase.reprocessPendingMessages(customerId, phoneNumber);
    } catch (error) {
      logger.error(`Error deleting customer: ${customerId} phone: ${error.message}`);
      throw new BusinessException(
        'CustomerPhoneUseCase:rebalanceCustomerPhones',
        `Error during rebalance of customer phones for customer ${customerId}: ${error.message}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }
  }

  private async rebalanceCustomerPhones(customerId: string, deletedPhoneWeight: number) {
    logger.info(`Rebalancing customer phones for customer: ${customerId}`);
    try {
      const remainingPhones = await this.customerPhoneAdapter.getAll({ customerId });

      if (remainingPhones.length === 0) {
        logger.info(`No remaining phones for customer ${customerId}, skipping rebalance.`);
        return;
      }

      const totalRemainingWeight = remainingPhones.reduce(
        (sum, phone) => sum + Number(phone.weight),
        0,
      );

      for (const phone of remainingPhones) {
        const weightIncrease = (Number(phone.weight) / totalRemainingWeight) * deletedPhoneWeight;
        phone.weight = Math.round(weightIncrease) + Number(phone.weight);
        await this.customerPhoneAdapter.update(phone);
      }
      logger.info(
        `Rebalanced weights for ${remainingPhones.length} phones for customer: ${customerId}`,
      );

      const updatedPhones = await this.customerPhoneAdapter.getAll({ customerId });
      const finalTotalWeight = updatedPhones.reduce((sum, phone) => sum + Number(phone.weight), 0);

      if (finalTotalWeight !== this.TARGET_TOTAL_WEIGHT) {
        const weightDifference = this.TARGET_TOTAL_WEIGHT - finalTotalWeight;
        const phoneToAdjust = updatedPhones.sort((a, b) => Number(b.weight) - Number(a.weight))[0];
        phoneToAdjust.weight = Number(phoneToAdjust.weight) + weightDifference;
        logger.info(
          `Adjusting weight of phone ${phoneToAdjust.phoneNumber} by ${weightDifference} to reach target total weight of ${this.TARGET_TOTAL_WEIGHT}`,
        );
        await this.customerPhoneAdapter.update(phoneToAdjust);
      }

      logger.info(
        `Phone weights after deletion and rebalance for customer ${customerId}: ` +
          JSON.stringify(
            updatedPhones.map(phone => ({
              phoneNumber: phone.phoneNumber,
              weight: phone.weight,
            })),
          ),
      );
    } catch (error) {
      logger.error(
        `Error during rebalance of customer phones for customer ${customerId}: ${error.message}`,
      );
      throw new BusinessException(
        'CustomerPhoneUseCase:rebalanceCustomerPhones',
        `Error during rebalance of customer phones for customer ${customerId}: ${error.message}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }
  }

  async deleteCustomerPhoneByCustomerIdAndPhoneAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void> {
    logger.info(
      `Deleting customer phones by customer id: ${customerId}, phone number: ${phoneNumber} and communication channel: ${communicationChannel}`,
    );

    await Promise.all([
      this.customerPhoneAdapter.deleteByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      ),
      this.customerPhoneDestinationAdapter.deleteAllByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      ),
    ]);
  }

  async getNextBalancedPhoneByCustomerIdAndCommunicationChannel(
    customerId: string,
    communicationChannel: CommunicationChannel,
  ): Promise<string> {
    logger.info(
      `Fetching next phone number for customer: ${customerId} and channel: ${communicationChannel}`,
    );

    const phoneNumbers =
      await this.customerPhoneDestinationAdapter.getNextPhoneByCustomerIdAndCommunicationChannel(
        customerId,
        communicationChannel,
      );

    logger.info(`Available phones for customer: ${customerId}: ${JSON.stringify(phoneNumbers)}`);
    if (!phoneNumbers || phoneNumbers.length === 0) {
      logger.error(
        `No phone numbers found for the given customer: ${customerId} and communication channel: ${communicationChannel}`,
      );
      return null;
    }

    try {
      const totalWeight = phoneNumbers.reduce((sum, n) => sum + n.weight, 0);
      if (totalWeight === 0) return null;
      const random = Math.random() * totalWeight;
      let cumulative = 0;
      for (const number of phoneNumbers) {
        cumulative += number.weight;
        if (random < cumulative) {
          logger.info(
            `Selected phone number for customer: ${customerId} and channel: ${communicationChannel}: ${number.phoneNumber}`,
          );
          return number.phoneNumber;
        }
      }
    } catch (error) {
      logger.error(
        `Unable to use round robin method in phone number balancing... Fall back to equal division method and selecting current less used phone: ${phoneNumbers[0].phoneNumber}`,
      );

      return phoneNumbers[0].phoneNumber;
    }
  }

  private getCustomerPhoneResponseDto(
    createdCustomerPhone: CustomerPhoneEntity,
  ): CustomerPhoneResponseDto {
    return plainToClass(CustomerPhoneResponseDto, createdCustomerPhone);
  }
}
