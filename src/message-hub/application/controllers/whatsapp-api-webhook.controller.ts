import { Body, Controller, Get, Post, Query, Res, Version } from '@nestjs/common';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { SystemAuthnGuard } from '@common/auth/system-authn.guard';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { Response } from 'express';
import { logger } from '@edutalent/commons-sdk';
import { WhatsAppCommonObject } from '@message-hub/misc/interfaces/whatsapp-api/webhook-common';
import { WhatsappApiWebhookUseCase } from '@message-hub/application/use-cases/whatsapp-api-webhook.use-case';
import { Public } from '@common/auth/decorators/public.decorator';

@ExcludeGuards(
  SystemAuthnGuard.name,
  AuthnGuard.name,
  AuthzAccountGuard.name,
  AuthzUserInAccountGuard.name,
)
@Controller('message-hub/whatsapp-api-webhook')
export class WhatsAppApiWebhookController {
  constructor(private readonly whatsappApiWebhookUseCase: WhatsappApiWebhookUseCase) {}

  @Public()
  @Get()
  @Version('1')
  async verifyWebhook(
    @Res() res: Response,
    @Query('hub.mode') mode: string,
    @Query('hub.verify_token') verifyToken: string,
    @Query('hub.challenge') challenge: string,
  ) {
    try {
      if (mode === 'subscribe' && verifyToken === process.env.WHATSAPP_VERIFY_TOKEN) {
        return res.status(200).send(challenge);
      }
      return res.status(403).send('Acesso não autorizado');
    } catch (error) {
      logger.error(
        `Erro ao lidar com a requisição GET de webhook de whatsapp: Erro - ${error}`,
        error,
      );
      return res.status(500).send('Erro ao processar a requisição');
    }
  }

  @Public()
  @Post()
  @Version('1')
  async handleWebhook(@Body() data: WhatsAppCommonObject, @Res() res: Response) {
    await this.whatsappApiWebhookUseCase.handleWebhook(data);
    return res.sendStatus(200);
  }
}
