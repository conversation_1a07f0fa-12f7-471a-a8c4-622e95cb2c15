import { Body, Controller, Delete, Get, Param, Post, Put, Version } from '@nestjs/common';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { CustomerPhoneDto } from '@message-hub/application/dto/in/customer-phone.dto';
import { CustomerPhoneUseCase } from '@message-hub/application/use-cases/customer-phone.use-case';
import { ApiUrlDto } from '@message-hub/application/dto/in/api-url.dto';
import { CommunicationChannel } from '@common/enums';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('message-hub/customers/:customerId/phones')
export class CustomerPhoneController {
  constructor(private readonly customerPhoneUseCase: CustomerPhoneUseCase) {}

  @Post()
  @Version('1')
  async createCustomerPhone(
    @Param('customerId') customerId: string,
    @Body() customerPhoneDto: CustomerPhoneDto,
  ): Promise<any> {
    const createdCustomerPhone = await this.customerPhoneUseCase.createCustomerPhone(
      customerId,
      customerPhoneDto,
    );

    return {
      statusCode: 201,
      data: createdCustomerPhone,
    };
  }

  @Get()
  @Version('1')
  async findByCustomerId(@Param('customerId') customerId: string): Promise<any> {
    const customerPhones = await this.customerPhoneUseCase.getCustomerPhonesByCustomerId(
      customerId,
    );

    return {
      statusCode: 200,
      data: customerPhones,
    };
  }

  @Get('/:phoneNumber')
  @Version('1')
  async findByCustomerIdAndPhoneNumber(
    @Param('customerId') customerId: string,
    @Param('phoneNumber') phoneNumber: string,
  ): Promise<any> {
    const customerPhones =
      await this.customerPhoneUseCase.getCustomerPhoneByCustomerIdAndPhoneNumber(
        customerId,
        phoneNumber,
      );

    return {
      statusCode: 200,
      data: customerPhones,
    };
  }

  @Get('/:phoneNumber/communication-channels/:communicationChannel')
  @Version('1')
  async findByCustomerIdAndPhoneNumberAndCommunicationChannel(
    @Param('customerId') customerId: string,
    @Param('phoneNumber') phoneNumber: string,
    @Param('communicationChannel') communicationChannel: CommunicationChannel,
  ): Promise<any> {
    const customerPhones =
      await this.customerPhoneUseCase.getCustomerPhoneByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      );

    return {
      statusCode: 200,
      data: customerPhones,
    };
  }

  @Put('/:phoneNumber/api-url')
  @Version('1')
  async updateSelfHostedLinkByCustomerIdAndPhoneNumber(
    @Param('customerId') customerId: string,
    @Param('phoneNumber') phoneNumber: string,
    @Body() apiUrlDto: ApiUrlDto,
  ): Promise<any> {
    const updatedCustomerPhone = await this.customerPhoneUseCase.updatePhoneNumberApiUrl(
      customerId,
      phoneNumber,
      apiUrlDto.communicationChannel,
      apiUrlDto.apiUrl,
    );

    return {
      statusCode: 200,
      data: updatedCustomerPhone,
    };
  }

  @Delete()
  @Version('1')
  async deleteAllByCustomerId(@Param('customerId') customerId: string): Promise<any> {
    await this.customerPhoneUseCase.deleteAllCustomerPhoneByCustomerId(customerId);

    return {
      statusCode: 200,
    };
  }

  @Delete('/:phoneNumber')
  @Version('1')
  @ApiOperation({
    summary: 'Delete customer phone by customer ID and phone number',
    description: 'Deletes a specific phone number associated with a customer ID.'
  })
  @ApiParam({
    name: 'customerId',
    description: 'The unique identifier of the customer',
    type: String,
    required: true,
    example: 'f619dfdc-1048-4964-bf29-8382f71d1792',
  })
  @ApiParam({
    name: 'phoneNumber',
    description: 'The phone number to delete',
    type: String,
    required: true,
    example: '5511912345678',
  })
  @ApiResponse({
    status: 200,
    description: 'Phone number successfully deleted',
    schema: {
      type: 'object',
      properties: {
        statusCode: {
          type: 'number',
          example: 200,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid customer ID or phone number format',
  })
  @ApiResponse({
    status: 404,
    description: 'Customer or phone number not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async deleteByCustomerIdAndPhoneNumber(
    @Param('customerId') customerId: string,
    @Param('phoneNumber') phoneNumber: string,
  ): Promise<any> {
    await this.customerPhoneUseCase.deleteCustomerPhoneByCustomerIdAndPhone(
      customerId,
      phoneNumber,
    );

    return {
      statusCode: 200,
    };
  }

  @Delete('/:phoneNumber/communication-channels/:communicationChannel')
  @Version('1')
  async deleteByCustomerIdAndPhoneNumberAndCommunicationChannel(
    @Param('customerId') customerId: string,
    @Param('phoneNumber') phoneNumber: string,
    @Param('communicationChannel') communicationChannel: CommunicationChannel,
  ): Promise<any> {
    await this.customerPhoneUseCase.deleteCustomerPhoneByCustomerIdAndPhoneAndCommunicationChannel(
      customerId,
      phoneNumber,
      communicationChannel,
    );

    return {
      statusCode: 200,
    };
  }
}
