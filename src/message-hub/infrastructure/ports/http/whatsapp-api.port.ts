import { DefaultOutgoingMessage } from '@message-hub/application/models/default-message.models';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';

export interface InfraWhatsAppApiPort {
  sendMessage(
    customerPhoneEntity: CustomerPhoneEntity,
    messageOutgoing: DefaultOutgoingMessage,
  ): Promise<void>;
  getMediaBuffer(customerPhoneEntity: CustomerPhoneEntity, mediaId: string): Promise<Buffer>;
}
