import { DbCommonPort } from '@common/db/ports/common.port';
import { IncomingMessageEntity } from '@message-hub/domain/entities/incoming-message.entity';
import { CommunicationChannel, MessageType } from '@common/enums';

export interface IncomingMessagePort extends DbCommonPort<IncomingMessageEntity> {
  processIncomingMessages(
    phoneNumberTo: string,
    channel: CommunicationChannel,
    executeItem: (
      from: string,
      to: string,
      message: string,
      messageTypes: MessageType[],
      channel: CommunicationChannel,
      filesUrl: string[],
    ) => Promise<void>,
  ): Promise<void>;
}
