import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { Prisma } from '@prisma/client';
import { IncomingMessageEntity } from '@message-hub/domain/entities/incoming-message.entity';
import { IncomingMessagePort } from '@message-hub/infrastructure/ports/db/incoming-message.port';
import { CommunicationChannel, MessageType } from '@common/enums';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class IncomingMessageAdapter
  extends PrismaCommonAdapter<IncomingMessageEntity>
  implements IncomingMessagePort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'incomingMessage');
  }

  async processIncomingMessages(
    phoneNumberTo: string,
    channel: CommunicationChannel,
    executeItem: (
      from: string,
      to: string,
      message: string,
      messageTypes: MessageType[],
      channel: CommunicationChannel,
      filesUrl: string[],
    ) => Promise<void>,
  ): Promise<void> {
    return this.prisma.client.$transaction(
      async tx => {
        const lockedIds = await tx.$queryRaw<{ id: string }[]>(Prisma.sql`
          SELECT im.id
          FROM message_hub.incoming_message im
          WHERE im.read = false
            AND im.read_at is null
            AND im.to = ${phoneNumberTo}
            AND im.communication_channel = ${channel}
          LIMIT 50
            FOR UPDATE SKIP LOCKED;
        `);

        if (!lockedIds || lockedIds.length === 0) {
          return;
        }

        const ids = lockedIds.map(lock => lock.id);
        const messages = await tx.$queryRaw<
          {
            from: string;
            to: string;
            channel: string;
            message_ids: string[];
            concatenated_messages: string;
            files_url: string[];
            message_types: MessageType[];
          }[]
        >(Prisma.sql`
          SELECT im.from,
                 im.to,
                 im.communication_channel                               as channel,
                 array_agg(id ORDER BY im.created_at)                   AS message_ids,
                 string_agg(im.message, ' ' ORDER BY im.created_at ASC) AS concatenated_messages,
                 array_agg(im.file_url ORDER BY im.created_at ASC)      AS files_url,
                 array_agg(im.message_type ORDER BY im.created_at ASC)  AS message_types
          FROM message_hub.incoming_message im
          WHERE im.read = false
            AND im.read_at is null
            AND im.id IN (${Prisma.join(ids.map(id => Prisma.sql`${id}::uuid`))})
            AND im.to = ${phoneNumberTo}
            AND im.communication_channel = ${channel}
          GROUP BY im.from, im.to, im.communication_channel;
        `);

        if (!messages || messages.length === 0) {
          return;
        }

        for (const message of messages) {
          try {
            const filteredUrls = message.files_url.filter(Boolean);

            executeItem(
              message.from,
              message.to,
              message.concatenated_messages,
              message.message_types,
              CommunicationChannel[message.channel as keyof typeof CommunicationChannel],
              filteredUrls,
            );

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE message_hub.incoming_message
                SET read       = true,
                    read_at    = NOW(),
                    updated_at = NOW()
                WHERE id IN (${Prisma.join(
                  message.message_ids.map(id => Prisma.sql`${id}::uuid`),
                )});
              `,
            );
          } catch (error) {
            logger.error(
              `Error processing incoming message: ${JSON.stringify(
                message,
              )} Error: ${JSON.stringify(error)}`,
              error,
            );
          }
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }
}
