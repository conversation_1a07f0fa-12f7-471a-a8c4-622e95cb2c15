import { CommunicationChannel, MessageType, RecordStatus } from '@common/enums';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class OutgoingMessageEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  readonly from?: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsEnum(MessageType)
  readonly messageType: MessageType;

  @IsString()
  @IsNotEmpty()
  readonly message: string;

  @IsEnum(CommunicationChannel)
  readonly channel: CommunicationChannel;

  @IsDate()
  @IsNotEmpty()
  readonly timeToGo: Date;

  @IsBoolean()
  @IsNotEmpty()
  readonly sent: boolean;

  @IsString()
  @IsNotEmpty()
  readonly apiUrl?: string;

  @IsDate()
  @IsNotEmpty()
  sentAt: Date;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsBoolean()
  @IsNotEmpty()
  readonly isfirstMessage: boolean;

  @IsOptional()
  @IsString()
  readonly fileUrl?: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(
    id: string,
    customerId: string,
    to: string,
    messageType: MessageType,
    message: string,
    channel: CommunicationChannel,
    timeToGo: Date,
    isFirstMessage: boolean,
    from?: string,
    apiUrl?: string,
    sent?: boolean,
    sentAt?: Date,
    status?: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
    fileUrl?: string,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.to = to;
    this.messageType = messageType;
    this.message = message;
    this.channel = channel;
    this.timeToGo = timeToGo;
    this.from = from;
    this.apiUrl = apiUrl;
    this.sent = sent;
    this.sentAt = sentAt;
    this.status = status;
    this.isfirstMessage = isFirstMessage;
    this.fileUrl = fileUrl;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
