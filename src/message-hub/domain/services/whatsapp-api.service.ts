import { logger } from '@edutalent/commons-sdk';
import { WhatsAppCommonObject } from '@message-hub/misc/interfaces/whatsapp-api/webhook-common';

export class WhatsappApiService {
  extractPhoneNumberIdFromUrl(url: string): string | null {
    try {
      const parsedUrl = new URL(url);
      const pathParts = parsedUrl.pathname.split('/').filter(Boolean);
      const phoneNumberId = pathParts[1];

      return /^\d+$/.test(phoneNumberId) ? phoneNumberId : null;
    } catch {
      return null;
    }
  }

  verifyIncomingMessage(data: WhatsAppCommonObject): {
    isIncomingMessage: boolean;
  } {
    const messageResponse = data.entry[0].changes[0].value.messages;
    return {
      isIncomingMessage: messageResponse !== undefined,
    };
  }

  adjustContactNumber(whatsappNumber: string): string {
    if (whatsappNumber.startsWith('55')) {
      whatsappNumber =
        whatsappNumber.length < 13
          ? `${whatsappNumber.slice(0, 4)}9${whatsappNumber.slice(4)}`
          : whatsappNumber;
    } else {
      logger.info(`International whatsapp whatsappNumber: ${whatsappNumber}`);
    }

    return whatsappNumber;
  }
}
