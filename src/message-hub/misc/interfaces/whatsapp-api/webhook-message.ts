export interface WhatsappMessageWebhook {
  from: string;
  id: string;
  timestamp: string;
  type: string;
  text?: {
    body: string;
  };
  audio?: {
    mime_type: string;
    sha256: string;
    id: string;
    voice: boolean;
  };
  context?: {
    from: string;
    id: string;
  };
  interactive?: {
    type: string;
    button_reply?: {
      id: string;
      title: string;
    };
    list_reply?: {
      id: string;
      title: string;
    };
  };
  button?: {
    payload: string;
    text: string;
  };
  document?: {
    filename: string;
    mime_type: string;
    sha256: string;
    id: string;
  };
}
