import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empty, <PERSON>Optional, IsString } from 'class-validator';
import { CommunicationChannel, MessageType } from '@common/enums';

export class ExecutePortfolioItemDto {
  @IsString()
  @IsNotEmpty({ message: 'from is required' })
  readonly from: string;

  @IsString()
  @IsNotEmpty({ message: 'to is required' })
  readonly to: string;

  @IsString()
  @IsOptional()
  readonly message?: string;

  @IsArray()
  @IsEnum(MessageType, { each: true })
  @IsNotEmpty({ message: 'messageTypes is required' })
  readonly messageTypes: MessageType[];

  @IsEnum(CommunicationChannel)
  @IsNotEmpty({ message: 'channel is required' })
  readonly channel: CommunicationChannel;

  @IsOptional()
  readonly filesUrl?: string[];

  constructor(
    from: string,
    to: string,
    messageTypes: MessageType[],
    channel: CommunicationChannel,
    message?: string,
    filesUrl?: string[],
  ) {
    this.from = from;
    this.to = to;
    this.message = message;
    this.messageTypes = messageTypes;
    this.channel = channel;
    this.filesUrl = filesUrl;
  }
}
