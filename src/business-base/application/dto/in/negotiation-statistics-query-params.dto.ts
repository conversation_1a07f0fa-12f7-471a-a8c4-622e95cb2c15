import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty } from 'class-validator';

export class NegotiationStatisticsQueryParamsDto {
  @ApiProperty({
    description: 'Start date for filtering negotiations (YYYY-MM-DD format)',
    example: '2024-01-01',
    required: true,
  })
  @IsDateString({}, { message: 'startDate must be a valid date string (YYYY-MM-DD format)' })
  @IsNotEmpty({ message: 'startDate is required' })
  readonly startDate: string;

  @ApiProperty({
    description: 'End date for filtering negotiations (YYYY-MM-DD format)',
    example: '2024-12-31',
    required: true,
  })
  @IsDateString({}, { message: 'endDate must be a valid date string (YYYY-MM-DD format)' })
  @IsNotEmpty({ message: 'endDate is required' })
  readonly endDate: string;
}
