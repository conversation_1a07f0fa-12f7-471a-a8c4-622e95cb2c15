import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class PortfolioNegotiationStatsDto {
  @ApiProperty({
    description: 'Portfolio ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID('4')
  readonly portfolioId: string;

  @ApiProperty({
    description: 'Count of active negotiations (NOT in final states: FAILED, CANCELLED, OPTED_OUT, FINISHED)',
    example: 15,
  })
  @IsNumber()
  readonly activeNegotiations: number;

  @ApiProperty({
    description: 'Count of in-progress negotiations (status = IN_PROGRESS)',
    example: 8,
  })
  @IsNumber()
  readonly inProgressNegotiations: number;

  @ApiProperty({
    description: 'Total count of negotiations (active + in-progress)',
    example: 23,
  })
  @IsNumber()
  readonly totalNegotiations: number;

  constructor(
    portfolioId: string,
    activeNegotiations: number,
    inProgressNegotiations: number,
  ) {
    this.portfolioId = portfolioId;
    this.activeNegotiations = activeNegotiations;
    this.inProgressNegotiations = inProgressNegotiations;
    this.totalNegotiations = activeNegotiations + inProgressNegotiations;
  }
}

export class NegotiationStatisticsSummaryDto {
  @ApiProperty({
    description: 'Total active negotiations across all portfolios',
    example: 150,
  })
  @IsNumber()
  readonly totalActiveNegotiations: number;

  @ApiProperty({
    description: 'Total in-progress negotiations across all portfolios',
    example: 80,
  })
  @IsNumber()
  readonly totalInProgressNegotiations: number;

  @ApiProperty({
    description: 'Total negotiations across all portfolios',
    example: 230,
  })
  @IsNumber()
  readonly totalNegotiations: number;

  constructor(
    totalActiveNegotiations: number,
    totalInProgressNegotiations: number,
  ) {
    this.totalActiveNegotiations = totalActiveNegotiations;
    this.totalInProgressNegotiations = totalInProgressNegotiations;
    this.totalNegotiations = totalActiveNegotiations + totalInProgressNegotiations;
  }
}

export class NegotiationStatisticsResponseDto {
  @ApiProperty({
    description: 'Negotiation statistics grouped by portfolio',
    type: [PortfolioNegotiationStatsDto],
  })
  @ValidateNested({ each: true })
  @Type(() => PortfolioNegotiationStatsDto)
  readonly portfolios: PortfolioNegotiationStatsDto[];

  @ApiProperty({
    description: 'Summary of all negotiation statistics',
    type: NegotiationStatisticsSummaryDto,
  })
  @ValidateNested()
  @Type(() => NegotiationStatisticsSummaryDto)
  readonly summary: NegotiationStatisticsSummaryDto;

  constructor(
    portfolios: PortfolioNegotiationStatsDto[],
    summary: NegotiationStatisticsSummaryDto,
  ) {
    this.portfolios = portfolios;
    this.summary = summary;
  }
}
