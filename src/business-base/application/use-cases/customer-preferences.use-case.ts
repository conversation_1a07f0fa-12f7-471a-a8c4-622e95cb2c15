import { Inject, Injectable } from '@nestjs/common';
import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { logger } from '@edutalent/commons-sdk';
import {
  CustomerPreferencesEntity,
  PortfolioPreferences,
  ExportConfig,
  StatsConfig,
} from '@business-base/domain/entities/customer-preferences.entity';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { RecordStatus } from '@common/enums';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';

@Injectable()
export class CustomerPreferencesUseCase {
  constructor(
    @Inject('CustomerPreferencesPort')
    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
    @Inject('InfraWorkflowPort')
    private readonly workflowAdapter: InfraWorkflowPort,
  ) { }

  async create(
    customerId: string,
    createCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<CustomerPreferencesEntity> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Creating customer preferences', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Check if preferences already exist
    const existingPreferences = await this.customerPreferencesAdapter.getById(customerId);
    if (existingPreferences) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences already exist for customerId: ${customerId}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }

    // Convert DTO (with workflowName) to Entity (with workflowId)
    let portfolioPreferences = new PortfolioPreferences();

    // Copy all non-config properties first
    if (createCustomerPreferencesDto.portfolio) {
      const { exportConfig, statsConfig, ...otherProps } = createCustomerPreferencesDto.portfolio;
      Object.assign(portfolioPreferences, otherProps);
    }

    portfolioPreferences = await this.handleConfigsWorkflowNameToId(createCustomerPreferencesDto, portfolioPreferences);

    // Extract dynamic properties (excluding portfolio to avoid conflicts)
    const { status, portfolio: _portfolio, ...dynamicProperties } = createCustomerPreferencesDto;

    const entity = new CustomerPreferencesEntity({
      customerId,
      portfolio: portfolioPreferences,
      ...dynamicProperties,
    });

    const createdEntity = await this.customerPreferencesAdapter.create(entity);

    logger.info('Customer preferences created successfully', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'USE_CASE',
    });

    return new CustomerPreferencesEntity({
      ...createdEntity, // Include all dynamic properties
      customerId,
    });
  }

  private async handleConfigsWorkflowNameToId(createCustomerPreferencesDto: CustomerPreferencesDto,
    portfolioPreferences: PortfolioPreferences,
  ): Promise<PortfolioPreferences> {
    // Handle exportConfig conversion
    if (createCustomerPreferencesDto.portfolio?.exportConfig) {
      portfolioPreferences.exportConfig = [];
      for (const config of createCustomerPreferencesDto.portfolio.exportConfig) {
        const workflow = await this.workflowAdapter.getWorkflowByName(config.workflowName);

        // Remove workflowName from config before storing, as we use workflowId as key
        const { workflowName, ...configWithoutWorkflowName } = config;
        portfolioPreferences.exportConfig.push(
          new ExportConfig(workflow.workflowId, configWithoutWorkflowName),
        );
      }
    }

    // Handle statsConfig conversion
    if (createCustomerPreferencesDto.portfolio?.statsConfig) {
      portfolioPreferences.statsConfig = [];
      for (const config of createCustomerPreferencesDto.portfolio.statsConfig) {
        const workflow = await this.workflowAdapter.getWorkflowByName(config.workflowName);
        const { workflowName, ...configWithoutWorkflowName } = config;
        portfolioPreferences.statsConfig.push(
          new StatsConfig(workflow.workflowId, configWithoutWorkflowName),
        );
      }
    }

    return portfolioPreferences;
  }

  async findById(customerId: string): Promise<CustomerPreferencesEntity | null> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Finding customer preferences by ID', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'USE_CASE',
    });

    const entity = await this.customerPreferencesAdapter.getById(customerId);

    if (!entity) {
      logger.warn('Customer preferences not found', {
        traceId,
        customerId,
        operation: 'findCustomerPreferencesById',
        layer: 'USE_CASE',
      });

      return null;
    }

    logger.info('Customer preferences found successfully', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'USE_CASE',
    });

    // Convert entity back to DTO (workflowId -> workflowName)
    let portfolioDto = undefined;
    if (entity.portfolio) {
      portfolioDto = { ...entity.portfolio };
    }

    return new CustomerPreferencesEntity({
      ...entity, // Include all dynamic properties
      customerId,
      portfolio: portfolioDto,
    });
  }

  async update(
    customerId: string,
    updateCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<CustomerPreferencesEntity> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Updating customer preferences', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Get existing preferences
    const existingEntity = await this.customerPreferencesAdapter.getByIdIgnoreStatus(customerId);
    if (!existingEntity) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences not found for customerId: ${customerId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    // Apply PUT semantics - replace with provided data only
    let updatedPortfolio = undefined;
    if (updateCustomerPreferencesDto.portfolio) {
      updatedPortfolio = new PortfolioPreferences();

      // Copy all non-config properties first
      const { exportConfig, statsConfig, ...otherProps } = updateCustomerPreferencesDto.portfolio;
      Object.assign(updatedPortfolio, otherProps);

      updatedPortfolio = await this.handleConfigsWorkflowNameToId(updateCustomerPreferencesDto, updatedPortfolio);
    }

    // Create updated entity with PUT semantics (replace entire resource)
    const { portfolio: _portfolio, ...dynamicProperties } = updateCustomerPreferencesDto;
    let status = updateCustomerPreferencesDto.status;

    if (!status) {
      status = RecordStatus.ACTIVE; // Default to ACTIVE if not provided
    }

    const updatedEntity = new CustomerPreferencesEntity({
      // Only include properties from the payload (PUT semantics)
      ...dynamicProperties,
      customerId,
      portfolio: updatedPortfolio,
      // Keep only essential system properties from existing entity
      status: status === RecordStatus.ACTIVE ? RecordStatus.ACTIVE : RecordStatus.DELETED,
      createdAt: existingEntity.createdAt,
      updatedAt: new Date(),
    });

    const result = await this.customerPreferencesAdapter.update(customerId, updatedEntity);

    logger.info('Customer preferences updated successfully', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'USE_CASE',
    });

    return new CustomerPreferencesEntity({
      ...result, // Include all dynamic properties
      customerId,
    });
  }

  async delete(customerId: string): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Deleting customer preferences', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Check if preferences exist
    const existingEntity = await this.customerPreferencesAdapter.getById(customerId);
    if (!existingEntity) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences not found for customerId: ${customerId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.customerPreferencesAdapter.delete(customerId);

    logger.info('Customer preferences deleted successfully', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'USE_CASE',
    });
  }
}
