import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import {
  NegotiationStatisticsResponseDto,
  PortfolioNegotiationStatsDto,
  NegotiationStatisticsSummaryDto,
} from '@business-base/application/dto/out/negotiation-statistics-response.dto';
import { RecordStatus } from '@common/enums';

@Injectable()
export class NegotiationStatisticsUseCase {
  constructor(
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
  ) { }

  async getNegotiationStatistics(
    customerId: string,
    portfolioId: string,
    startDate: string,
    endDate: string,
  ): Promise<NegotiationStatisticsResponseDto> {
    // Validate that the portfolio exists and belongs to the customer
    const portfolio = await this.portfolioAdapter.get(portfolioId);
    if (!portfolio || portfolio.customerId !== customerId || portfolio.status !== RecordStatus.ACTIVE) {
      throw new NotFoundException(`Portfolio with ID ${portfolioId} not found or does not belong to customer`);
    }

    // Parse and validate date range
    const parsedStartDate = new Date(startDate);
    const parsedEndDate = new Date(endDate);

    if (parsedStartDate >= parsedEndDate) {
      throw new Error('startDate must be before endDate');
    }

    // Set time boundaries for proper filtering
    parsedStartDate.setUTCHours(0, 0, 0, 0);
    parsedEndDate.setUTCHours(23, 59, 59, 999);

    // Get negotiation statistics for the specified portfolio
    const portfolioStats = await this.portfolioItemAdapter.getNegotiationStatistics(
      customerId,
      portfolioId,
      parsedStartDate,
      parsedEndDate,
    );

    // Create portfolio statistics DTO
    const portfolioStatsDto = new PortfolioNegotiationStatsDto(
      portfolioStats.portfolioId,
      portfolioStats.activeNegotiations,
      portfolioStats.inProgressNegotiations,
    );

    // Create summary (in this case, it's the same as the single portfolio stats)
    const summaryDto = new NegotiationStatisticsSummaryDto(
      portfolioStats.activeNegotiations,
      portfolioStats.inProgressNegotiations,
    );

    return new NegotiationStatisticsResponseDto([portfolioStatsDto], summaryDto);
  }
}
