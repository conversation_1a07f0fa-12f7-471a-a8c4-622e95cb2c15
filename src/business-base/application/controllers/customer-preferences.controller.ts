import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Version,
  UsePipes,
  ValidationPipe,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { UserRoleInAccount, AccountRole } from '@common/enums';
import {
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CustomerPreferencesOperations } from '@business-base/_docs/public-api/customer-preferences.operations';
import { ExcludeGuards } from '../../../common/auth/decorators/exclude-guard.decorator';
import { AuthnGuard } from '../../../common/auth/authn.guard';
import { AuthzAccountGuard } from '../../../common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '../../../common/auth/authz-user-in-account.guard';
import { SystemAuthnGuard } from '../../../common/auth/system-authn.guard';

@ApiTags('Customer Preferences')
@Controller('business-base/customer-preferences')
export class CustomerPreferencesController {
  constructor(private readonly customerPreferencesUseCase: CustomerPreferencesUseCase) { }

  @CustomerPreferencesOperations.create()
  @Post('')
  @Version('1')
  @UsePipes(
    new ValidationPipe({
      whitelist: false,
      forbidNonWhitelisted: false,
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  )
  @ApiBearerAuth()
  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @AccountRoles(AccountRole.BASIC)
  async create(
    @Req() request: Request,
    @Body() createCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const customerId = request['user'].customerId;

    logger.info('Creating customer preferences via HTTP', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'CONTROLLER',
    });

    const customerPreferences = await this.customerPreferencesUseCase.create(
      customerId,
      createCustomerPreferencesDto,
    );

    logger.info('Customer preferences created successfully via HTTP', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 201,
      data: customerPreferences,
    };
  }

  @CustomerPreferencesOperations.getByCustomerId()
  @Get('')
  @Version('1')
  @ApiBearerAuth()
  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @AccountRoles(AccountRole.BASIC)
  async findById(@Req() request: Request,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const customerId = request['user'].customerId;

    logger.info('Finding customer preferences by ID via HTTP', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'CONTROLLER',
    });

    const customerPreferences = await this.customerPreferencesUseCase.findById(customerId);

    logger.info('Customer preferences found successfully via HTTP', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 200,
      data: customerPreferences,
    };
  }

  @CustomerPreferencesOperations.update()
  @Put('')
  @Version('1')
  @UsePipes(
    new ValidationPipe({
      whitelist: false,
      forbidNonWhitelisted: false,
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  )
  @ApiBearerAuth()
  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @AccountRoles(AccountRole.BASIC)
  async update(
    @Req() request: Request,
    @Body() updateCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const customerId = request['user'].customerId;

    logger.info('Updating customer preferences via HTTP', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'CONTROLLER',
    });

    const updatedCustomerPreferences = await this.customerPreferencesUseCase.update(
      customerId,
      updateCustomerPreferencesDto,
    );

    logger.info('Customer preferences updated successfully via HTTP', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 200,
      data: updatedCustomerPreferences,
    };
  }

  @CustomerPreferencesOperations.delete()
  @Delete('')
  @Version('1')
  @ApiBearerAuth()
  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @AccountRoles(AccountRole.BASIC)
  async deleteById(@Req() request: Request,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const customerId = request['user'].customerId;

    logger.info('Deleting customer preferences via HTTP', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'CONTROLLER',
    });

    await this.customerPreferencesUseCase.delete(customerId);

    logger.info('Customer preferences deleted successfully via HTTP', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 200,
      data: { message: 'Customer preferences deleted successfully' },
    };
  }
}
