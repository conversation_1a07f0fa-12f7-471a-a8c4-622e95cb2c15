import { PortfolioItemCustomDataEntity } from '@business-base/domain/entities/portfolio-item-custom-data.entity';

export interface PortfolioItemCustomDataPort {
  getByPortfolioItemId(id: string, portfolioItemId: string): Promise<PortfolioItemCustomDataEntity>;

  create(entity: PortfolioItemCustomDataEntity): Promise<PortfolioItemCustomDataEntity>;

  batchGetByPortfolioItemIds(
    items: Array<{ id: string; portfolioItemId: string }>,
  ): Promise<PortfolioItemCustomDataEntity[]>;
}
