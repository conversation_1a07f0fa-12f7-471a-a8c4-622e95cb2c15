import { DbCommonPort } from '@common/db/ports/common.port';
import { PortfolioEntity } from '@business-base/domain/entities/portfolio.entity';
import { Paginated } from '@common/pagination/paginated';

export interface PortfolioPort extends DbCommonPort<PortfolioEntity> {
  findAllExecutingByCustomerId(customerId: string): Promise<PortfolioEntity[]>;
  updateTotalQuantity(id: string, totalQuantity: number): Promise<void>;
  findDefaultByCustomerId(customerId: string): Promise<PortfolioEntity | null>;
  findAllPortfoliosToFinish(): Promise<{ id: string }[]>;
  findAllWithSearchAndPagination(
    customerId: string,
    name?: string,
    page?: number,
    limit?: number,
  ): Promise<Paginated<PortfolioEntity>>;
}
