import { Injectable } from '@nestjs/common';
import { PortfolioEntity } from '@business-base/domain/entities/portfolio.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { Paginated } from '@common/pagination/paginated';
import {
  CommunicationChannel,
  PortfolioExecutionStatus,
  PortfolioImportStatus,
  PortfolioItemStatus,
  RecordStatus,
} from '@common/enums';

@Injectable()
export class PortfolioAdapter
  extends PrismaCommonAdapter<PortfolioEntity>
  implements PortfolioPort {
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'portfolio');
  }

  async findAllExecutingByCustomerId(customerId: string): Promise<PortfolioEntity[]> {
    const portfolios = await this.prisma.client.portfolio.findMany({
      where: {
        customerId,
        executionStatus: { not: PortfolioExecutionStatus.CANCELLED },
        importStatus: PortfolioImportStatus.SUCCESS,
        importFinishedAt: { not: null },
        status: RecordStatus.ACTIVE,
      },
    });

    return portfolios.map(portfolio => ({
      ...portfolio,
      status: portfolio.status as RecordStatus,
      executionStatus: portfolio.executionStatus as PortfolioExecutionStatus,
      importStatus: portfolio.importStatus as PortfolioImportStatus,
      communicationChannel: portfolio.communicationChannel as CommunicationChannel,
      isDefault: Boolean(portfolio.isDefault),
      totalQuantity: Number(portfolio.totalQuantity),
      processedQuantity: Number(portfolio.processedQuantity),
      totalSuccessQuantity: Number(portfolio.totalSuccessQuantity),
      totalFailedQuantity: Number(portfolio.totalFailedQuantity),
    }));
  }

  async updateTotalQuantity(id: string, totalQuantity: number): Promise<void> {
    await this.prisma.client.portfolio.update({
      where: { id },
      data: { totalQuantity },
    });
  }

  async findDefaultByCustomerId(customerId: string): Promise<PortfolioEntity | null> {
    const portfolio = await this.prisma.client.portfolio.findFirst({
      where: {
        customerId,
        isDefault: true,
        status: RecordStatus.ACTIVE,
      },
    });

    if (!portfolio) {
      return null;
    }

    return {
      ...portfolio,
      status: portfolio.status as RecordStatus,
      executionStatus: portfolio.executionStatus as PortfolioExecutionStatus,
      importStatus: portfolio.importStatus as PortfolioImportStatus,
      communicationChannel: portfolio.communicationChannel as CommunicationChannel,
      isDefault: Boolean(portfolio.isDefault),
      totalQuantity: Number(portfolio.totalQuantity),
      processedQuantity: Number(portfolio.processedQuantity),
      totalSuccessQuantity: Number(portfolio.totalSuccessQuantity),
      totalFailedQuantity: Number(portfolio.totalFailedQuantity),
    };
  }

  async findAllPortfoliosToFinish(): Promise<{ id: string }[]> {
    const portfolioIdsToFinish = await this.prisma.client.$queryRaw<{ id: string }[]>`
        SELECT p.id
        FROM business_base.portfolio p
        WHERE p.status = ${RecordStatus.ACTIVE}
          AND p.import_status = ${PortfolioImportStatus.SUCCESS}  
          AND p.import_finished_at IS NOT NULL
          AND p.follow_up_workflow_id IS NOT NULL
          AND p.execution_status NOT IN (${PortfolioExecutionStatus.INBOUND},
                                         ${PortfolioExecutionStatus.CANCELLED},
                                         ${PortfolioExecutionStatus.FINISHED},
                                         ${PortfolioExecutionStatus.PAUSED})
          AND NOT EXISTS (SELECT 1
                          FROM business_base.portfolio_item pi
                          WHERE pi.portfolio_id = p.id
                            AND pi.current_status IN
                                (${PortfolioItemStatus.IN_PROGRESS}, 
                                 ${PortfolioItemStatus.FOLLOWED_UP},
                                 ${PortfolioItemStatus.PENDING}));
    `;

    return portfolioIdsToFinish;
  }

  async findAllWithSearchAndPagination(
    customerId: string,
    name?: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<Paginated<PortfolioEntity>> {
    const where = {
      customerId,
      status: RecordStatus.ACTIVE,
      ...(name && {
        name: {
          contains: name,
          mode: 'insensitive' as const,
        },
      }),
    };

    const skip = (page - 1) * limit;

    const [portfolios, total] = await Promise.all([
      this.prisma.client.portfolio.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.client.portfolio.count({ where }),
    ]);

    const mappedPortfolios = portfolios.map(portfolio => ({
      ...portfolio,
      status: portfolio.status as RecordStatus,
      executionStatus: portfolio.executionStatus as PortfolioExecutionStatus,
      importStatus: portfolio.importStatus as PortfolioImportStatus,
      communicationChannel: portfolio.communicationChannel as CommunicationChannel,
      isDefault: Boolean(portfolio.isDefault),
      totalQuantity: Number(portfolio.totalQuantity),
      processedQuantity: Number(portfolio.processedQuantity),
      totalSuccessQuantity: Number(portfolio.totalSuccessQuantity),
      totalFailedQuantity: Number(portfolio.totalFailedQuantity),
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      data: mappedPortfolios,
      total,
      limit,
      page,
      totalPages,
    };
  }
}
