import { Injectable } from '@nestjs/common';
import { DynamoDBDocumentClient, GetCommand, PutCommand, BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { PortfolioItemCustomDataEntity } from '@business-base/domain/entities/portfolio-item-custom-data.entity';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class PortfolioItemCustomDataAdapter implements PortfolioItemCustomDataPort {
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_portfolio_items_custom_data';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async getByPortfolioItemId(
    id: string,
    portfolioItemId: string,
  ): Promise<PortfolioItemCustomDataEntity> {
    try {
      const { Item } = await this.dynamoClient.send(
        new GetCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            id: id.toString(),
            portfolioItemId: portfolioItemId.toString(),
          },
        }),
      );

      return Item as PortfolioItemCustomDataEntity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while fetching portfolio item custom data for id: ${id}, portfolioItemId: ${portfolioItemId}. Error: ${error.message || error
          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
          }`,
        error: error.message || error.toString(),
      });
    }
  }

  async create(entity: PortfolioItemCustomDataEntity): Promise<PortfolioItemCustomDataEntity> {
    try {
      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            portfolioItemId: entity.portfolioItemId,
            customData: entity.customData,
          },
        }),
      );
      return entity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while saving portfolio item custom data for id: ${entity.id
          }, portfolioItemId: ${entity.portfolioItemId}. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'
          }${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''}`,
        error: error.message || error.toString(),
      });
    }
  }

  async batchGetByPortfolioItemIds(
    items: Array<{ id: string; portfolioItemId: string }>,
  ): Promise<PortfolioItemCustomDataEntity[]> {
    if (items.length === 0) {
      return [];
    }

    // DynamoDB BatchGetItem has a limit of 100 items per request
    const BATCH_SIZE = 100;
    const results: PortfolioItemCustomDataEntity[] = [];
    const totalBatches = Math.ceil(items.length / BATCH_SIZE);
    let totalRequestsMade = 0;

    logger.info('Starting batch get operation for custom data', {
      totalItems: items.length,
      batchSize: BATCH_SIZE,
      estimatedBatches: totalBatches,
      tableName: this.DYNAMO_TABLE_NAME,
      operation: 'batchGetByPortfolioItemIds',
    });

    try {
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);

        const requestItems = {
          [this.DYNAMO_TABLE_NAME]: {
            Keys: batch.map(item => ({
              id: item.id.toString(),
              portfolioItemId: item.portfolioItemId.toString(),
            })),
          },
        };

        const { Responses, UnprocessedKeys } = await this.dynamoClient.send(
          new BatchGetCommand({
            RequestItems: requestItems,
          }),
        );

        totalRequestsMade++;

        if (Responses && Responses[this.DYNAMO_TABLE_NAME]) {
          results.push(...(Responses[this.DYNAMO_TABLE_NAME] as PortfolioItemCustomDataEntity[]));
        }

        if (UnprocessedKeys && Object.keys(UnprocessedKeys).length > 0) {
          logger.warn('Some items were not processed in batch get operation', {
            unprocessedCount: UnprocessedKeys[this.DYNAMO_TABLE_NAME]?.Keys?.length || 0,
            tableName: this.DYNAMO_TABLE_NAME,
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
          });
        }
      }

      // Log cost optimization metrics
      const individualRequestsAvoided = items.length - totalRequestsMade;
      const costSavingsPercent = ((individualRequestsAvoided / items.length) * 100).toFixed(2);

      logger.info('Batch get operation completed for custom data', {
        totalItems: items.length,
        itemsRetrieved: results.length,
        batchRequestsMade: totalRequestsMade,
        individualRequestsAvoided,
        costSavingsPercent: `${costSavingsPercent}%`,
        tableName: this.DYNAMO_TABLE_NAME,
        operation: 'batchGetByPortfolioItemIds',
      });

      return results;
    } catch (error) {
      throw new DynamoException({
        message: `Error while batch fetching portfolio item custom data. Error: ${error.message || error
          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
          }`,
        error: error.message || error.toString(),
      });
    }
  }
}
