import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { MessageHubOutgoingMessagePort } from '@business-base/infrastructure/ports/db/message-hub-outgoing-message.port';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class MessageHubOutgoingMessageAdapter implements MessageHubOutgoingMessagePort {
  private readonly messageHubServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.messageHubServiceUrl = String(process.env.MESSAGEHUB_SERVICE_URL);
  }

  async getOutgoingMessagesByCustomerId(
    customerId: string,
    to: string,
  ): Promise<
    {
      sent: boolean;
      sent_at: Date;
      time_to_go: Date;
      message: string;
      message_type: string;
      to: string;
      from: string;
    }[]
  > {
    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/outgoing-messages/customer/${customerId}/${to}`;
      logger.info(`Getting outgoing messages for customer: ${customerId}, to: ${to}`);

      const { data } = await lastValueFrom(this.httpService.get(url));

      return data.data.map(message => ({
        sent: message.sent,
        sent_at: message.sent_at ? new Date(message.sent_at) : null,
        time_to_go: message.time_to_go ? new Date(message.time_to_go) : null,
        message: message.message,
        message_type: message.message_type,
        to: message.to,
        from: message.from,
        is_first_message: message.is_first_message,
      }));
    } catch (error) {
      handleHttpError(error, 'message-hub-outgoing-message-adapter');
    }
  }
}
