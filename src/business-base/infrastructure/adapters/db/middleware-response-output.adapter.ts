import { Injectable } from '@nestjs/common';
import { DynamoDBDocumentClient, PutCommand, BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { MiddlewareResponseOutputEntity } from '@business-base/domain/entities/middleware-response-output.entity';
import { logger } from '@edutalent/commons-sdk';
import { DynamoLoggingService } from '@common/dynamo/dynamo-logging.service';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class MiddlewareResponseOutputAdapter implements MiddlewareResponseOutputPort {
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly dynamoLoggingService: DynamoLoggingService;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_middleware_response_output';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
    this.dynamoLoggingService = new DynamoLoggingService(this.dynamoClient);
  }

  async getByPortfolioItemId(
    id: string,
    portfolioItemId: string,
  ): Promise<MiddlewareResponseOutputEntity> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Getting middleware response output', {
      traceId,
      operation: 'getByPortfolioItemId',
      id,
      portfolioItemId,
      timestamp: new Date().toISOString(),
      layer: 'ADAPTER',
    });

    try {
      const { Item } = await this.dynamoLoggingService.get({
        TableName: this.DYNAMO_TABLE_NAME,
        Key: {
          id,
          portfolioItemId,
        },
      });

      if (Item) {
        logger.info('Middleware response output retrieved successfully', {
          traceId,
          operation: 'getByPortfolioItemId',
          id,
          portfolioItemId,
          dataSize: JSON.stringify(Item.data || {}).length,
          timestamp: new Date().toISOString(),
          layer: 'ADAPTER',
        });
      } else {
        logger.info('Middleware response output not found', {
          traceId,
          operation: 'getByPortfolioItemId',
          id,
          portfolioItemId,
          timestamp: new Date().toISOString(),
          layer: 'ADAPTER',
        });
      }

      return Item as MiddlewareResponseOutputEntity;
    } catch (error) {
      logger.error('Error getting middleware response output', {
        traceId,
        operation: 'getByPortfolioItemId',
        id,
        portfolioItemId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        layer: 'ADAPTER',
      });

      throw new DynamoException({
        message: `Error while fetching portfolio item middleware response output for id: ${id}, portfolioItemId: ${portfolioItemId}. Error: ${error.message || error
          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
          }`,
        error: error.message || error.toString(),
      });
    }
  }

  async update(
    id: string,
    portfolioItemId: string,
    data: any,
  ): Promise<MiddlewareResponseOutputEntity> {
    logger.info(
      `Updating middleware response output for id: ${id} and portfolio item id: ${portfolioItemId}. Data: ${JSON.stringify(
        data,
      )}`,
    );

    const entity = await this.getByPortfolioItemId(id, portfolioItemId);

    const updatedEntityData = { ...entity.data, ...data };

    logger.info(
      `Updated middleware response output for id: ${id} and portfolio item id: ${portfolioItemId}. Data: ${JSON.stringify(
        updatedEntityData,
      )}`,
    );

    try {
      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            portfolioItemId: entity.portfolioItemId,
            data: updatedEntityData,
          },
        }),
      );
      return entity;
    } catch (error) {
      logger.error(
        `Error updating middleware response output for id: ${id} and portfolio item id: ${portfolioItemId} and Data: ${JSON.stringify(
          data,
        )}. Error: ${JSON.stringify(error)}`,
      );
      throw new DynamoException({
        message: `Error while updating middleware response output for id: ${id}, portfolioItemId: ${portfolioItemId}. Error: ${error.message || error
          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
          }`,
        error: error.message || error.toString(),
      });
    }
  }

  async create(entity: MiddlewareResponseOutputEntity): Promise<MiddlewareResponseOutputEntity> {
    try {
      logger.info(
        `Creating middleware response output for id: ${JSON.stringify(
          entity,
        )}. Data: ${JSON.stringify(entity)}`,
      );

      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            portfolioItemId: entity.portfolioItemId,
            data: entity.data,
          },
        }),
      );
      return entity;
    } catch (error) {
      logger.error(
        `Error saving middleware response output  and portfolio item id: ${JSON.stringify(
          entity,
        )}. Error: ${JSON.stringify(error)}`,
      );

      throw new DynamoException({
        message: `Error while creating middleware response output for id: ${entity.id
          }, portfolioItemId: ${entity.portfolioItemId}. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'
          }${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''}`,
        error: error.message || error.toString(),
      });
    }
  }

  /**
   * Batch get multiple middleware response output records
   * DynamoDB BatchGetItem supports up to 100 items per request
   * Cost-optimized: Reduces requests by ~99% compared to individual GetItem calls
   */
  async batchGetByPortfolioItemIds(
    items: Array<{ id: string; portfolioItemId: string }>,
  ): Promise<MiddlewareResponseOutputEntity[]> {
    if (items.length === 0) {
      return [];
    }

    const traceId = CorrelationContextService.getTraceId();
    const BATCH_SIZE = 100;
    const results: MiddlewareResponseOutputEntity[] = [];
    const totalBatches = Math.ceil(items.length / BATCH_SIZE);
    let totalRequestsMade = 0;

    logger.info('Starting batch get operation for middleware response outputs', {
      traceId,
      operation: 'batchGetByPortfolioItemIds',
      totalItems: items.length,
      batchSize: BATCH_SIZE,
      estimatedBatches: totalBatches,
      tableName: this.DYNAMO_TABLE_NAME,
      timestamp: new Date().toISOString(),
      layer: 'ADAPTER',
    });

    try {
      // Process items in batches of 100
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);

        const requestItems = {
          [this.DYNAMO_TABLE_NAME]: {
            Keys: batch.map(item => ({
              id: item.id.toString(),
              portfolioItemId: item.portfolioItemId.toString(),
            })),
          },
        };

        const { Responses, UnprocessedKeys } = await this.dynamoClient.send(
          new BatchGetCommand({
            RequestItems: requestItems,
          }),
        );

        totalRequestsMade++;

        // Add successful responses to results
        if (Responses && Responses[this.DYNAMO_TABLE_NAME]) {
          results.push(...(Responses[this.DYNAMO_TABLE_NAME] as MiddlewareResponseOutputEntity[]));
        }

        // Handle unprocessed keys
        if (UnprocessedKeys && Object.keys(UnprocessedKeys).length > 0) {
          logger.warn('Some middleware response outputs were not processed in batch get operation', {
            traceId,
            unprocessedCount: UnprocessedKeys[this.DYNAMO_TABLE_NAME]?.Keys?.length || 0,
            tableName: this.DYNAMO_TABLE_NAME,
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
          });
        }
      }

      // Log cost optimization metrics
      const individualRequestsAvoided = items.length - totalRequestsMade;
      const costSavingsPercent = ((individualRequestsAvoided / items.length) * 100).toFixed(2);

      logger.info('Batch get middleware response outputs completed', {
        traceId,
        operation: 'batchGetByPortfolioItemIds',
        totalItems: items.length,
        itemsRetrieved: results.length,
        batchRequestsMade: totalRequestsMade,
        individualRequestsAvoided,
        costSavingsPercent: `${costSavingsPercent}%`,
        tableName: this.DYNAMO_TABLE_NAME,
        timestamp: new Date().toISOString(),
        layer: 'ADAPTER',
      });

      return results;
    } catch (error) {
      logger.error('Error batch getting middleware response outputs', {
        traceId,
        operation: 'batchGetByPortfolioItemIds',
        itemCount: items.length,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        layer: 'ADAPTER',
      });

      throw new DynamoException({
        message: `Error while batch fetching middleware response outputs. Error: ${error.message || error
          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
          }`,
        error: error.message || error.toString(),
      });
    }
  }
}
