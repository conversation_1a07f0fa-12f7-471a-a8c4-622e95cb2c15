import { Injectable } from '@nestjs/common';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { RecordStatus } from '@common/enums';
import { Prisma } from '@prisma/client';

@Injectable()
export class CollectCashStatsAdapter
  extends PrismaCommonAdapter<CollectCashStatsEntity>
  implements CollectCashStatsPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'collectCashStats');
  }

  async getAverageTicketByCustomerIdWithDateRange(customerId: string, startDate?: Date, endDate?: Date): Promise<number> {
    const whereClause: any = {
      customerId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _avg: {
        dealValue: true,
      },
    });

    return Number(result._avg.dealValue) || 0;
  }
  
  async getAverageTicketByPortfolioIdWithDateRange(portfolioId: string, startDate?: Date, endDate?: Date): Promise<number> {
    const whereClause: any = {
      portfolioId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _avg: {
        dealValue: true,
      },
    });

    return Number(result._avg.dealValue) || 0;
  }

  async findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]> {
    const stats = await this.prisma.client.collectCashStats.findMany({
      where: {
        customerId,
        status: RecordStatus.ACTIVE,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async getTotalDealValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      customerId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _sum: {
        dealValue: true,
      },
    });

    return Number(result._sum.dealValue) || 0;
  }

  async getTotalDealValueByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      portfolioId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _sum: {
        dealValue: true,
      },
    });

    return Number(result._sum.dealValue) || 0;
  }

  private mapToEntity(stat: any): CollectCashStatsEntity {
    return new CollectCashStatsEntity(
      stat.customerId,
      stat.portfolioId,
      stat.portfolioItemId,
      stat.workflowId,
      Number(stat.dealValue), // Already in cents from database
      Number(stat.originalDebt), // Already in cents from database
      stat.installments,
      stat.status as RecordStatus,
      stat.createdAt,
      stat.updatedAt,
    );
  }
}
