import {
  IsUUID,
  IsNotEmpty,
  IsString,
  IsDate,
  IsArray,
  IsNumber,
  IsPositive,
  ValidateNested,
  Matches,
  IsOptional,
  IsEnum,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { RecordStatus } from '@common/enums';
import { StatsDataSource } from '@business-base/application/dto/customer-preferences.dto';

export class TaxRules {
  @IsString()
  @IsOptional()
  penaltyFee?: string;

  @IsString()
  @IsOptional()
  dailyFee?: string;

  constructor(data?: Partial<TaxRules>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class CustomImportConfig {
  delimiter?: string;
  taxRules?: TaxRules;
  headerMapping?: Record<string, string>;
  additionalHeaders?: string[];

  constructor(data?: Partial<CustomImportConfig>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}
export class ImportTransformer {
  name?: string;
  tax?: number;
  fee?: number;

  constructor(data?: Partial<ImportTransformer>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class PortfolioPreferences {
  defaultWorkflowId?: string;
  timezoneUTC?: string;
  importCronExpression?: string;
  followUpWorkflowId?: string;
  followUpCronExpression?: string;
  followUpQuantity?: number;
  followUpIntervalMinutes?: number;
  exportColumns?: string[];
  daysBeforePaymentReminder?: number;
  paymentReminderInDay?: boolean;
  customImportConfig?: CustomImportConfig;
  statsConfig?: StatsConfig[];
  exportConfig?: ExportConfig[];

  // Allow dynamic properties
  [key: string]: any;

  constructor(data?: Partial<PortfolioPreferences>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class StatsFieldConfig {
  readonly source: StatsDataSource;
  readonly path: string;

  constructor(source: StatsDataSource, path: string) {
    this.source = source;
    this.path = path;
  }
}

export class StatsConfig {
  readonly workflowId: string;
  readonly dealValue?: StatsFieldConfig;
  readonly originalDebt?: StatsFieldConfig;
  readonly installments?: StatsFieldConfig;

  constructor(workflowId: string, ...dynamicFields: any[]) {
    this.workflowId = workflowId;

    // Handle additional dynamic statistical fields
    if (dynamicFields && dynamicFields.length > 0) {
      Object.assign(this, dynamicFields[0]);
    }
  }
}

export class ExportConfig {
  readonly workflowId: string;

  constructor(workflowId: string, ...dynamicFields: any[]) {
    this.workflowId = workflowId;

    // Handle additional dynamic export column fields
    if (dynamicFields && dynamicFields.length > 0) {
      Object.assign(this, dynamicFields[0]);
    }
  }
}

export class CustomerPreferencesEntity {
  @IsUUID('4')
  @IsOptional()
  customerId?: string;

  @ValidateNested()
  @Type(() => PortfolioPreferences)
  @IsOptional()
  portfolio?: PortfolioPreferences;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(data: Partial<CustomerPreferencesEntity>) {
    // Set required defaults
    this.status = data.status || RecordStatus.ACTIVE;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();

    // Assign all properties dynamically
    Object.assign(this, data);
  }
}
