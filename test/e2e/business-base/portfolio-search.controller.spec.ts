import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { randomUUID as uuidv4 } from 'crypto';
import { getAuthCredentials } from 'test/helpers/authenticated-user';
import { CommunicationChannel, MessageType } from '@common/enums';
import { PortfolioDto } from '@business-base/application/dto/in/portfolio.dto';

describe('Portfolio Search Controller (e2e)', () => {
  let app: INestApplication;
  let testWorkflowId: string;
  let testCustomer: { login: string; password: string; token: string; customerId: string };
  const testPortfolios: string[] = [];

  beforeAll(async () => {
    app = global.__NEST_APP__;

    // Create test customer with unique identifiers
    const uniqueId = uuidv4().substring(0, 8);
    testCustomer = await createCustomerFake(
      `Portfolio Search Test Customer ${uniqueId}`,
      `portfolio_search_test_${uniqueId}`,
      `12.345.678/0001-${Math.floor(Math.random() * 90) + 10}`,
    );

    // Create agent for workflow with unique name
    const agent = {
      role: 'portfolio-search-test-agent',
      backstory: 'I am an agent that processes portfolio search test data.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
      name: `portfolio-search-agent-${uniqueId}`,
    };

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    const agentId = createAgentResponse.body.data.id;

    // Create task for workflow
    const task = {
      description:
        'Process portfolio search test data for {{name}} with phone {{phone_number}} and value {{value}} on {{date}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    const taskId = createTaskResponse.body.data.id;

    // Create workflow with unique name
    const workflow = {
      name: `Portfolio Search Test Workflow ${uniqueId}`,
      description: 'Test workflow for portfolio search functionality',
      steps: [
        {
          description: 'Process portfolio search test data',
          order: 1,
          taskId: taskId,
        },
      ],
    };

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    testWorkflowId = createWorkflowResponse.body.data.workflowId;

    // Create multiple test portfolios with different names for search testing
    const portfolioNames = [
      'Alpha Portfolio Test',
      'Beta Portfolio Search',
      'Gamma Investment Fund',
      'Alpha Beta Combined',
      'Delta Portfolio Management',
      'Search Test Portfolio',
      'Investment Alpha Strategy',
      'Beta Search Fund',
    ];

    for (const name of portfolioNames) {
      const portfolio = createPortfolioFake(name, 5, false);

      const { body: portfolioResponse } = await request(app.getHttpServer())
        .post('/api/v1/business-base/portfolios/import')
        .attach('file', `${__dirname}/data/test-portfolio.csv`)
        .field('name', portfolio.name)
        .field('workflowId', testWorkflowId)
        .field('workExpression', portfolio.workExpression)
        .field('executeImmediately', portfolio.executeImmediately.toString())
        .field('idleAfter', portfolio.idleAfter.toString())
        .field('communicationChannel', CommunicationChannel.WHATSAPPSELFHOSTED)
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(201);

      testPortfolios.push(portfolioResponse.data.id);
    }
  });

  describe('/v1/business-base/portfolios/search (GET)', () => {
    it('should return all portfolios with default pagination when no parameters provided', async () => {
      const { body } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);
      expect(body.data).toBeInstanceOf(Array);
      expect(body.total).toBeGreaterThan(0);
      expect(body.limit).toBe(10);
      expect(body.page).toBe(1);
      expect(body.totalPages).toBeGreaterThan(0);
      expect(body.data.length).toBeLessThanOrEqual(10);

      // Verify portfolio structure
      if (body.data.length > 0) {
        const portfolio = body.data[0];
        expect(portfolio).toHaveProperty('id');
        expect(portfolio).toHaveProperty('name');
        expect(portfolio).toHaveProperty('dealValue');
        expect(portfolio).toHaveProperty('customerId');
        expect(portfolio).toHaveProperty('executionStatus');
        expect(portfolio).toHaveProperty('importStatus');
      }
    });

    it('should search portfolios by name (case-insensitive partial match)', async () => {
      const { body } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?name=alpha')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);
      expect(body.data).toBeInstanceOf(Array);
      expect(body.total).toBeGreaterThan(0);

      // All returned portfolios should contain "alpha" in their name (case-insensitive)
      body.data.forEach(portfolio => {
        expect(portfolio.name.toLowerCase()).toContain('alpha');
      });
    });

    it('should search portfolios by name with exact match', async () => {
      const { body } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?name=Beta Portfolio Search')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);
      expect(body.data).toBeInstanceOf(Array);
      expect(body.total).toBe(1);
      expect(body.data[0].name).toBe('Beta Portfolio Search');
    });

    it('should return empty results for non-existent search term', async () => {
      const { body } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?name=NonExistentPortfolio')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);
      expect(body.data).toBeInstanceOf(Array);
      expect(body.data.length).toBe(0);
      expect(body.total).toBe(0);
      expect(body.totalPages).toBe(0);
    });

    it('should handle pagination correctly', async () => {
      // First page
      const { body: page1 } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?page=1&limit=3')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(page1.statusCode).toBe(200);
      expect(page1.data).toBeInstanceOf(Array);
      expect(page1.data.length).toBeLessThanOrEqual(3);
      expect(page1.page).toBe(1);
      expect(page1.limit).toBe(3);

      // Second page (if exists)
      if (page1.totalPages > 1) {
        const { body: page2 } = await request(app.getHttpServer())
          .get('/api/v1/business-base/portfolios/search?page=2&limit=3')
          .set('Authorization', `Bearer ${testCustomer.token}`)
          .expect(200);

        expect(page2.statusCode).toBe(200);
        expect(page2.page).toBe(2);
        expect(page2.limit).toBe(3);

        // Ensure different results between pages
        const page1Ids = page1.data.map(p => p.id);
        const page2Ids = page2.data.map(p => p.id);
        expect(page1Ids).not.toEqual(page2Ids);
      }
    });

    it('should combine search and pagination', async () => {
      const { body } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?name=portfolio&page=1&limit=2')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);
      expect(body.data).toBeInstanceOf(Array);
      expect(body.data.length).toBeLessThanOrEqual(2);
      expect(body.page).toBe(1);
      expect(body.limit).toBe(2);

      // All results should contain "portfolio" in name
      body.data.forEach(portfolio => {
        expect(portfolio.name.toLowerCase()).toContain('portfolio');
      });
    });

    it('should validate pagination parameters', async () => {
      // Invalid page (less than 1)
      const { body: invalidPage } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?page=0')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(400);

      expect(invalidPage.statusCode).toBe(400);

      // Invalid limit (greater than 100)
      const { body: invalidLimit } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?limit=101')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(400);

      expect(invalidLimit.statusCode).toBe(400);
    });

    it('should handle date filtering with pagination', async () => {
      const startDate = '2024-01-01T00:00:00.000Z';
      const endDate = '2024-12-31T23:59:59.999Z';

      const { body } = await request(app.getHttpServer())
        .get(
          `/api/v1/business-base/portfolios/search?startDate=${startDate}&endDate=${endDate}&page=1&limit=5`,
        )
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);
      expect(body.data).toBeInstanceOf(Array);
      expect(body.page).toBe(1);
      expect(body.limit).toBe(5);

      // Verify deal value is present (filtered by date)
      body.data.forEach(portfolio => {
        expect(portfolio).toHaveProperty('dealValue');
        expect(typeof portfolio.dealValue).toBe('string');
      });
    });

    it('should return 401 for unauthenticated requests', async () => {
      await request(app.getHttpServer()).get('/api/v1/business-base/portfolios/search').expect(401);
    });

    it('should only return portfolios for the authenticated customer', async () => {
      // Create another customer with unique identifiers
      const otherUniqueId = uuidv4().substring(0, 8);
      const otherCustomer = await createCustomerFake(
        `Other Customer ${otherUniqueId}`,
        `other_customer_${otherUniqueId}`,
        `98.765.432/0001-${Math.floor(Math.random() * 90) + 10}`,
      );

      // Get portfolios for original customer
      const { body: originalCustomerPortfolios } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      // Get portfolios for other customer (should be empty or different)
      const { body: otherCustomerPortfolios } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search')
        .set('Authorization', `Bearer ${otherCustomer.token}`)
        .expect(200);

      // Verify customer isolation
      const originalIds = originalCustomerPortfolios.data.map(p => p.id);
      const otherIds = otherCustomerPortfolios.data.map(p => p.id);

      // No portfolio IDs should overlap between customers
      const intersection = originalIds.filter(id => otherIds.includes(id));
      expect(intersection.length).toBe(0);
    });

    it('should maintain consistent sorting (createdAt desc)', async () => {
      const { body } = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/search?limit=5')
        .set('Authorization', `Bearer ${testCustomer.token}`)
        .expect(200);

      expect(body.statusCode).toBe(200);

      if (body.data.length > 1) {
        // Verify sorting by createdAt descending
        for (let i = 0; i < body.data.length - 1; i++) {
          const current = new Date(body.data[i].createdAt);
          const next = new Date(body.data[i + 1].createdAt);
          expect(current.getTime()).toBeGreaterThanOrEqual(next.getTime());
        }
      }
    });
  });

  async function createCustomerFake(
    name: string,
    nickname: string,
    cnpj: string,
  ): Promise<{ login: string; password: string; token: string; customerId: string }> {
    // Use multiple uniqueness factors to avoid conflicts in CI environment
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2);
    const processId = process.pid || Math.floor(Math.random() * 10000);
    const uniqueEmail = `user.${timestamp}.${randomId}.${processId}@example.com`;
    const uniqueNickname = `${nickname}_${timestamp}_${randomId}`;
    const uniqueCnpj = `${cnpj.substring(0, 12)}${String(timestamp).slice(-2)}`;
    // Generate a valid Brazilian phone number format: +5511NNNNNNNNN (11 digits after +55)
    const phoneNumber = `987${String(timestamp).slice(-6)}`; // 9 digits total
    const uniquePhone = `+5511${phoneNumber}`;

    const customerUserRequest = {
      customerData: {
        cnpj: uniqueCnpj,
        email: uniqueEmail,
        name: `${name} ${timestamp}`,
        phone: uniquePhone,
        whatsappPhone: uniquePhone,
        segment: 'collectcash',
      },
      accountData: {
        nickname: uniqueNickname,
      },
      userData: {
        email: uniqueEmail,
        firstname: `${name} ${timestamp}`,
        lastname: 'Doe',
        password: 'P@ssw0rd123',
        passwordConfirmation: 'P@ssw0rd123',
      },
    };

    const { body } = await request(app.getHttpServer())
      .post('/api/v1/auth/users/signup')
      .send(customerUserRequest)
      .expect(201);

    const { accessToken } = await getAuthCredentials(app, uniqueEmail, 'P@ssw0rd123');

    return {
      login: uniqueEmail,
      password: 'P@ssw0rd123',
      token: accessToken,
      customerId: body.data.customer.id,
    };
  }

  function createPortfolioFake(
    name: string,
    idleAfter: number,
    executeImmediately: boolean = false,
  ): PortfolioDto {
    return new PortfolioDto(
      name,
      testWorkflowId,
      '*/5 * * * *',
      CommunicationChannel.WHATSAPPSELFHOSTED,
      idleAfter,
      executeImmediately,
    );
  }
});
